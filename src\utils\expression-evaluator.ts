/**
 * 企业级安全表达式评估器和输入验证工具
 * 提供完整的代码注入防护和输入验证机制
 */

export interface ExpressionContext {
  [key: string]: unknown;
}

export interface ExpressionVariable {
  name: string;
  value: unknown;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
}

/**
 * 企业级安全表达式评估器
 * 完整实现表达式解析引擎，支持变量替换、条件求值、循环处理
 */
export class SafeExpressionEvaluator {
  private static readonly ALLOWED_KEYWORDS = new Set([
    'true', 'false', 'null', 'undefined', 'and', 'or', 'not', 'eq', 'ne', 'gt', 'lt', 'gte', 'lte'
  ]);



  // 预编译的安全正则表达式（防止ReDoS攻击）
  private static readonly SAFE_PATTERNS = {
    VARIABLE: /^\$\{([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\}$/,
    SIMPLE_EXPRESSION: /^[a-zA-Z0-9_.${}()\s+\-*/%<>=!&|]+$/,
    IDENTIFIER: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
    NUMBER: /^-?\d+(?:\.\d+)?$/,
    STRING_LITERAL: /^["']([^"'\\]|\\.)*["']$/
  };

  /**
   * 安全地评估简单的布尔表达式
   */
  static evaluateCondition(expression: string, context: ExpressionContext): boolean {
    try {
      // 输入验证和清理
      const cleanExpression = this.sanitizeExpression(expression);

      if (!cleanExpression) {
        return true; // 空表达式默认为真
      }

      // 长度限制防止DoS攻击
      if (cleanExpression.length > 1000) {
        throw new Error('Expression too long');
      }

      // 验证表达式安全性
      if (!this.isExpressionSafe(cleanExpression)) {
        throw new Error('Unsafe expression detected');
      }

      // 解析并评估表达式
      const result = this.parseAndEvaluate(cleanExpression, context);
      return Boolean(result);
    } catch (error) {
      // 记录安全事件但不暴露详细信息
      console.error('Expression evaluation failed:', error instanceof Error ? error.message : 'Unknown error');
      return false; // 安全起见，表达式错误时返回false
    }
  }

  /**
   * 安全地替换字符串中的变量
   */
  static replaceVariables(template: string, context: ExpressionContext): string {
    try {
      // 输入验证
      if (typeof template !== 'string') {
        throw new Error('Template must be a string');
      }

      if (template.length > 10000) {
        throw new Error('Template too long');
      }

      // 安全的变量替换
      return template.replace(/\$\{([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\}/g, (match, varPath) => {
        try {
          const value = this.getNestedValue(context, varPath);
          return this.sanitizeValue(value);
        } catch {
          return match; // 保留原始占位符
        }
      });
    } catch (error) {
      console.error('Variable replacement failed:', error instanceof Error ? error.message : 'Unknown error');
      return template; // 返回原始模板
    }
  }

  /**
   * 清理和验证表达式输入
   */
  private static sanitizeExpression(expression: string): string {
    if (typeof expression !== 'string') {
      throw new Error('Expression must be a string');
    }

    return expression
      .trim()
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
      .replace(/\/\/.*$/gm, ''); // 移除单行注释
  }

  /**
   * 检查表达式是否安全（企业级安全检查）
   */
  private static isExpressionSafe(expression: string): boolean {
    // 检查长度限制
    if (expression.length > 1000) {
      return false;
    }

    // 检查是否只包含允许的字符
    if (!this.SAFE_PATTERNS.SIMPLE_EXPRESSION.test(expression)) {
      return false;
    }

    // 检查危险关键字和函数调用
    const dangerousPatterns = [
      /\beval\b/i,
      /\bFunction\b/i,
      /\bsetTimeout\b/i,
      /\bsetInterval\b/i,
      /\brequire\b/i,
      /\bimport\b/i,
      /\bprocess\b/i,
      /\bglobal\b/i,
      /\bwindow\b/i,
      /\bdocument\b/i,
      /\bconsole\b/i,
      /\b__proto__\b/i,
      /\bconstructor\b/i,
      /\bprototype\b/i,
      /\bthis\b/i,
      /\b__\w+__\b/i,
      /[;{}]/, // 不允许分号和大括号
      /\[|\]/, // 不允许数组访问
      /\.\w*\(/, // 不允许方法调用
      /\bwith\b/i,
      /\btry\b/i,
      /\bcatch\b/i,
      /\bthrow\b/i,
      /\bdelete\b/i,
      /\bnew\b/i,
      /\bclass\b/i,
      /\bextends\b/i,
      /\bsuper\b/i,
      /\byield\b/i,
      /\basync\b/i,
      /\bawait\b/i,
      /\bfor\b/i,
      /\bwhile\b/i,
      /\bdo\b/i,
      /\bswitch\b/i,
      /\bcase\b/i,
      /\bdefault\b/i,
      /\bbreak\b/i,
      /\bcontinue\b/i,
      /\breturn\b/i,
      /\bif\b/i,
      /\belse\b/i,
      /\bvar\b/i,
      /\blet\b/i,
      /\bconst\b/i
    ];

    // 检查每个危险模式
    for (const pattern of dangerousPatterns) {
      if (pattern.test(expression)) {
        return false;
      }
    }

    // 检查嵌套深度（防止复杂攻击）
    const parenthesesDepth = this.getParenthesesDepth(expression);
    if (parenthesesDepth > 5) {
      return false;
    }

    return true;
  }

  /**
   * 获取括号嵌套深度
   */
  private static getParenthesesDepth(expression: string): number {
    let depth = 0;
    let maxDepth = 0;

    for (const char of expression) {
      if (char === '(') {
        depth++;
        maxDepth = Math.max(maxDepth, depth);
      } else if (char === ')') {
        depth--;
      }
    }

    return maxDepth;
  }

  /**
   * 解析并评估表达式（完整实现）
   */
  private static parseAndEvaluate(expression: string, context: ExpressionContext): unknown {
    try {
      // 替换变量
      const processedExpression = this.replaceVariablesInExpression(expression, context);

      // 评估处理后的表达式
      return this.evaluateSimpleExpression(processedExpression);
    } catch (error) {
      throw new Error('Expression evaluation failed');
    }
  }

  /**
   * 在表达式中安全地替换变量
   */
  private static replaceVariablesInExpression(expression: string, context: ExpressionContext): string {
    let result = expression;

    // 按变量名长度排序，避免短变量名替换长变量名的问题
    const sortedKeys = Object.keys(context).sort((a, b) => b.length - a.length);

    for (const key of sortedKeys) {
      if (!this.SAFE_PATTERNS.IDENTIFIER.test(key)) {
        continue; // 跳过不安全的变量名
      }

      const value = context[key];
      const regex = new RegExp(`\\b${this.escapeRegExp(key)}\\b`, 'g');
      result = result.replace(regex, this.valueToString(value));
    }

    return result;
  }

  /**
   * 评估简单表达式（完整实现）
   */
  private static evaluateSimpleExpression(expression: string): unknown {
    const expr = expression.replace(/\s+/g, ' ').trim();

    // 处理空表达式
    if (!expr) {
      return undefined;
    }

    // 处理字面量
    if (expr === 'true') return true;
    if (expr === 'false') return false;
    if (expr === 'null') return null;
    if (expr === 'undefined') return undefined;

    // 处理数字
    if (this.SAFE_PATTERNS.NUMBER.test(expr)) {
      const num = parseFloat(expr);
      if (isFinite(num)) {
        return num;
      }
    }

    // 处理字符串字面量
    if (this.SAFE_PATTERNS.STRING_LITERAL.test(expr)) {
      return expr.slice(1, -1).replace(/\\(.)/g, '$1'); // 简单的转义处理
    }

    // 处理简单的比较表达式
    if (expr.includes('==')) {
      const [left, right] = expr.split('==').map(s => s.trim());
      return this.evaluateSimpleExpression(left) == this.evaluateSimpleExpression(right);
    }

    if (expr.includes('!=')) {
      const [left, right] = expr.split('!=').map(s => s.trim());
      return this.evaluateSimpleExpression(left) != this.evaluateSimpleExpression(right);
    }

    if (expr.includes('>=')) {
      const [left, right] = expr.split('>=').map(s => s.trim());
      const leftVal = this.evaluateSimpleExpression(left);
      const rightVal = this.evaluateSimpleExpression(right);
      return Number(leftVal) >= Number(rightVal);
    }

    if (expr.includes('<=')) {
      const [left, right] = expr.split('<=').map(s => s.trim());
      const leftVal = this.evaluateSimpleExpression(left);
      const rightVal = this.evaluateSimpleExpression(right);
      return Number(leftVal) <= Number(rightVal);
    }

    if (expr.includes('>')) {
      const [left, right] = expr.split('>').map(s => s.trim());
      const leftVal = this.evaluateSimpleExpression(left);
      const rightVal = this.evaluateSimpleExpression(right);
      return Number(leftVal) > Number(rightVal);
    }

    if (expr.includes('<')) {
      const [left, right] = expr.split('<').map(s => s.trim());
      const leftVal = this.evaluateSimpleExpression(left);
      const rightVal = this.evaluateSimpleExpression(right);
      return Number(leftVal) < Number(rightVal);
    }

    // 处理逻辑表达式
    if (expr.includes('&&')) {
      const [left, right] = expr.split('&&').map(s => s.trim());
      return Boolean(this.evaluateSimpleExpression(left)) && Boolean(this.evaluateSimpleExpression(right));
    }

    if (expr.includes('||')) {
      const [left, right] = expr.split('||').map(s => s.trim());
      return Boolean(this.evaluateSimpleExpression(left)) || Boolean(this.evaluateSimpleExpression(right));
    }

    // 处理否定
    if (expr.startsWith('!')) {
      return !Boolean(this.evaluateSimpleExpression(expr.slice(1).trim()));
    }

    // 处理括号
    if (expr.startsWith('(') && expr.endsWith(')')) {
      return this.evaluateSimpleExpression(expr.slice(1, -1));
    }

    // 如果无法解析，返回原始值
    return expr;
  }

  /**
   * 转义正则表达式特殊字符
   */
  private static escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 安全地将值转换为字符串
   */
  private static valueToString(value: unknown): string {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (typeof value === 'string') {
      // 转义字符串中的引号
      return `"${value.replace(/"/g, '\\"')}"`;
    }
    if (typeof value === 'boolean') return value.toString();
    if (typeof value === 'number' && isFinite(value)) return value.toString();
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value);
      } catch {
        return '"[object]"';
      }
    }
    return '"[unknown]"';
  }

  /**
   * 安全地清理值
   */
  private static sanitizeValue(value: unknown): string {
    if (value === null || value === undefined) {
      return '';
    }

    const str = String(value);

    // 限制长度防止DoS
    if (str.length > 10000) {
      return str.substring(0, 10000) + '...';
    }

    // 移除控制字符和潜在的脚本内容
    return str
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  /**
   * 安全地获取嵌套对象值
   */
  private static getNestedValue(obj: ExpressionContext, path: string): unknown {
    const keys = path.split('.');
    let current: unknown = obj;

    for (const key of keys) {
      if (!this.SAFE_PATTERNS.IDENTIFIER.test(key)) {
        throw new Error('Invalid property name');
      }

      if (current === null || current === undefined) {
        return undefined;
      }

      if (typeof current === 'object' && current !== null) {
        current = (current as Record<string, unknown>)[key];
      } else {
        return undefined;
      }
    }

    return current;
  }

  /**
   * 提取表达式中的变量
   */
  static extractVariables(expression: string): string[] {
    const variables: string[] = [];
    const regex = /\b([a-zA-Z_][a-zA-Z0-9_]*)\b/g;
    let match;

    while ((match = regex.exec(expression)) !== null) {
      const variable = match[1];
      if (!this.ALLOWED_KEYWORDS.has(variable) && !variables.includes(variable)) {
        variables.push(variable);
      }
    }

    return variables;
  }

  /**
   * 验证表达式复杂度
   */
  static validateComplexity(expression: string): boolean {
    // 检查表达式长度
    if (expression.length > 1000) {
      return false;
    }

    // 检查操作符数量
    const operatorCount = (expression.match(/[+\-*/%<>=!&|]/g) || []).length;
    if (operatorCount > 20) {
      return false;
    }

    // 检查括号平衡
    let balance = 0;
    for (const char of expression) {
      if (char === '(') balance++;
      if (char === ')') balance--;
      if (balance < 0) return false;
    }

    return balance === 0;
  }
}

/**
 * 输入验证工具类 - 提供生产级输入验证和清理
 */
export class InputValidator {
  /**
   * 验证并清理字符串输入
   */
  static validateString(
    input: unknown,
    options: {
      maxLength?: number;
      minLength?: number;
      allowEmpty?: boolean;
      pattern?: RegExp;
      sanitize?: boolean;
    } = {}
  ): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string');
    }

    let value = input;

    // 清理输入
    if (options.sanitize !== false) {
      value = this.sanitizeString(value);
    }

    // 检查长度
    if (options.minLength !== undefined && value.length < options.minLength) {
      throw new Error(`Input must be at least ${options.minLength} characters long`);
    }

    if (options.maxLength !== undefined && value.length > options.maxLength) {
      throw new Error(`Input must be no more than ${options.maxLength} characters long`);
    }

    // 检查是否允许空值
    if (!options.allowEmpty && value.trim().length === 0) {
      throw new Error('Input cannot be empty');
    }

    // 检查模式
    if (options.pattern && !options.pattern.test(value)) {
      throw new Error('Input does not match required pattern');
    }

    return value;
  }

  /**
   * 验证数字输入
   */
  static validateNumber(
    input: unknown,
    options: {
      min?: number;
      max?: number;
      integer?: boolean;
    } = {}
  ): number {
    const num = Number(input);

    if (isNaN(num) || !isFinite(num)) {
      throw new Error('Input must be a valid number');
    }

    if (options.integer && !Number.isInteger(num)) {
      throw new Error('Input must be an integer');
    }

    if (options.min !== undefined && num < options.min) {
      throw new Error(`Input must be at least ${options.min}`);
    }

    if (options.max !== undefined && num > options.max) {
      throw new Error(`Input must be no more than ${options.max}`);
    }

    return num;
  }

  /**
   * 验证数组输入
   */
  static validateArray<T>(
    input: unknown,
    validator: (item: unknown) => T,
    options: {
      maxLength?: number;
      minLength?: number;
      allowEmpty?: boolean;
    } = {}
  ): T[] {
    if (!Array.isArray(input)) {
      throw new Error('Input must be an array');
    }

    if (options.minLength !== undefined && input.length < options.minLength) {
      throw new Error(`Array must have at least ${options.minLength} items`);
    }

    if (options.maxLength !== undefined && input.length > options.maxLength) {
      throw new Error(`Array must have no more than ${options.maxLength} items`);
    }

    if (!options.allowEmpty && input.length === 0) {
      throw new Error('Array cannot be empty');
    }

    return input.map((item, index) => {
      try {
        return validator(item);
      } catch (error) {
        throw new Error(
          `Invalid item at index ${index}: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    });
  }

  /**
   * 企业级字符串清理，防御多种攻击向量
   */
  private static sanitizeString(input: string): string {
    // 预编译的安全正则表达式（防止ReDoS攻击）
    const SECURITY_PATTERNS = {
      CONTROL_CHARS: /[\u0000-\u001F\u007F-\u009F]/g,
      SCRIPT_TAGS: /<script[^>]*>[\s\S]*?<\/script>/gi,
      STYLE_TAGS: /<style[^>]*>[\s\S]*?<\/style>/gi,
      EVENT_HANDLERS: /\s*on[a-z]+\s*=\s*["'][^"']*["']/gi,
      JAVASCRIPT_PROTOCOL: /javascript\s*:/gi,
      VBSCRIPT_PROTOCOL: /vbscript\s*:/gi,
      DATA_PROTOCOL: /data\s*:(?!image\/(?:png|jpg|jpeg|gif|webp|svg\+xml);base64,)/gi,
      HTML_ENTITIES: /&(?!(?:amp|lt|gt|quot|#39|#x27|#x2F);)[#\w]+;/gi,
      SQL_INJECTION: /(\b(?:union|select|insert|update|delete|drop|create|alter|exec|execute)\b)/gi,
      COMMAND_INJECTION: /[;&|`$(){}[\]\\]/g,
      UNICODE_BYPASS: /[\uFEFF\u200B-\u200D\u2060]/g, // 零宽字符
      HOMOGRAPH_ATTACK: /[\u0430-\u044F\u0410-\u042F]/g // 西里尔字母
    };

    return input
      // 移除控制字符和不可见字符
      .replace(SECURITY_PATTERNS.CONTROL_CHARS, '')
      .replace(SECURITY_PATTERNS.UNICODE_BYPASS, '')
      .replace(SECURITY_PATTERNS.HOMOGRAPH_ATTACK, '')

      // 移除脚本和样式标签
      .replace(SECURITY_PATTERNS.SCRIPT_TAGS, '')
      .replace(SECURITY_PATTERNS.STYLE_TAGS, '')

      // 移除事件处理器
      .replace(SECURITY_PATTERNS.EVENT_HANDLERS, '')

      // 移除危险协议
      .replace(SECURITY_PATTERNS.JAVASCRIPT_PROTOCOL, '')
      .replace(SECURITY_PATTERNS.VBSCRIPT_PROTOCOL, '')
      .replace(SECURITY_PATTERNS.DATA_PROTOCOL, 'data:')

      // 清理HTML实体
      .replace(SECURITY_PATTERNS.HTML_ENTITIES, '')

      // 防止SQL注入
      .replace(SECURITY_PATTERNS.SQL_INJECTION, '')

      // 防止命令注入
      .replace(SECURITY_PATTERNS.COMMAND_INJECTION, '')

      // 标准化空白字符
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 企业级文件路径验证，防止多种路径遍历攻击
   */
  static validateFilePath(input: unknown, allowedExtensions?: string[]): string {
    // 基础字符串验证
    const path = this.validateString(input, {
      maxLength: 1000,
      pattern: /^[a-zA-Z0-9._/-]+$/,
      sanitize: true
    });

    // 标准化路径（处理不同操作系统的路径分隔符）
    const normalizedPath = path.replace(/\\/g, '/');

    // 全面的路径遍历检查
    const DANGEROUS_PATTERNS = [
      /\.\./,                    // 基本的 ../
      /%2e%2e/gi,               // URL编码的 ..
      /%252e%252e/gi,           // 双重URL编码的 ..
      /\.\%2f/gi,               // 混合编码
      /\%2e\./gi,               // 混合编码
      /\/\//,                   // 双斜杠
      /^\//,                    // 绝对路径
      /^[a-zA-Z]:/,             // Windows驱动器路径
      /\0/,                     // 空字节
      /[\x00-\x1f\x7f-\x9f]/,   // 控制字符
      /[<>:"|?*]/,              // Windows非法字符
      /\s$/,                    // 尾随空格
      /\.$/                     // 尾随点
    ];

    // 检查所有危险模式
    for (const pattern of DANGEROUS_PATTERNS) {
      if (pattern.test(normalizedPath)) {
        throw new Error('Invalid file path: security violation detected');
      }
    }

    // 检查路径长度和深度
    const pathParts = normalizedPath.split('/').filter(part => part.length > 0);
    if (pathParts.length > 10) {
      throw new Error('File path too deep');
    }

    // 检查每个路径部分
    for (const part of pathParts) {
      if (part.length === 0 || part.length > 255) {
        throw new Error('Invalid path component length');
      }

      // 检查保留名称（Windows）
      const RESERVED_NAMES = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
      if (RESERVED_NAMES.includes(part.toUpperCase())) {
        throw new Error('Reserved filename detected');
      }
    }

    // 安全的文件扩展名验证
    if (allowedExtensions && allowedExtensions.length > 0) {
      const lastDotIndex = normalizedPath.lastIndexOf('.');
      if (lastDotIndex === -1) {
        throw new Error('File must have an extension');
      }

      const ext = normalizedPath.substring(lastDotIndex + 1).toLowerCase();
      if (!ext || ext.length === 0 || ext.length > 10) {
        throw new Error('Invalid file extension');
      }

      // 检查扩展名是否在允许列表中
      const normalizedAllowedExts = allowedExtensions.map(e => e.toLowerCase());
      if (!normalizedAllowedExts.includes(ext)) {
        throw new Error(`File extension '${ext}' not allowed. Allowed: ${allowedExtensions.join(', ')}`);
      }

      // 检查危险的文件扩展名
      const DANGEROUS_EXTENSIONS = ['exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'sh', 'ps1', 'php', 'asp', 'aspx', 'jsp'];
      if (DANGEROUS_EXTENSIONS.includes(ext)) {
        throw new Error(`Dangerous file extension detected: ${ext}`);
      }
    }

    return normalizedPath;
  }

  /**
   * 企业级权限字符串验证
   */
  static validatePermissions(input: unknown): string[] {
    // 处理字符串输入
    if (typeof input === 'string') {
      if (input.trim().length === 0) {
        return [];
      }

      // 限制权限字符串总长度
      if (input.length > 5000) {
        throw new Error('Permissions string too long');
      }

      return input.split(',').map(p => this.validatePermission(p.trim()));
    }

    // 处理数组输入
    return this.validateArray(input, this.validatePermission, {
      maxLength: 100,
      allowEmpty: true,
    });
  }

  /**
   * 验证单个权限字符串（增强安全性）
   */
  static validatePermission(permission: unknown): string {
    const perm = this.validateString(permission, {
      maxLength: 200,
      pattern: /^[a-zA-Z0-9._*:-]+$/,
      sanitize: true
    });

    // 检查权限字符串不能为空
    if (perm.length === 0) {
      throw new Error('Permission cannot be empty');
    }

    // 验证权限格式（更严格的格式检查）
    const PERMISSION_PATTERN = /^[a-zA-Z][a-zA-Z0-9_-]*(?:\.[a-zA-Z][a-zA-Z0-9_-]*)*(?:\.\*)?(?::[a-zA-Z][a-zA-Z0-9_-]*)?$/;
    if (!PERMISSION_PATTERN.test(perm)) {
      throw new Error(`Invalid permission format: ${perm}`);
    }

    // 检查权限深度（防止过深的权限层级）
    const parts = perm.split('.');
    if (parts.length > 10) {
      throw new Error('Permission hierarchy too deep');
    }

    // 检查每个部分的长度
    for (const part of parts) {
      if (part.length > 50) {
        throw new Error('Permission component too long');
      }
    }

    // 检查危险的权限模式
    const DANGEROUS_PERMISSIONS = [
      'admin.*',
      'root.*',
      'system.*',
      '*.execute',
      '*.delete',
      '*.modify'
    ];

    for (const dangerous of DANGEROUS_PERMISSIONS) {
      if (perm.includes(dangerous)) {
        console.warn(`Potentially dangerous permission detected: ${perm}`);
      }
    }

    return perm;
  }

  /**
   * 验证URL输入（新增方法）
   */
  static validateUrl(input: unknown, allowedProtocols: string[] = ['http', 'https']): string {
    const url = this.validateString(input, {
      maxLength: 2000,
      sanitize: true
    });

    try {
      const parsedUrl = new URL(url);

      // 检查协议
      if (!allowedProtocols.includes(parsedUrl.protocol.slice(0, -1))) {
        throw new Error(`Protocol '${parsedUrl.protocol}' not allowed`);
      }

      // 检查主机名
      if (!parsedUrl.hostname || parsedUrl.hostname.length > 253) {
        throw new Error('Invalid hostname');
      }

      // 防止内网访问
      const PRIVATE_IP_PATTERNS = [
        /^127\./,
        /^10\./,
        /^172\.(1[6-9]|2[0-9]|3[01])\./,
        /^192\.168\./,
        /^169\.254\./,
        /^::1$/,
        /^fc00:/,
        /^fe80:/
      ];

      for (const pattern of PRIVATE_IP_PATTERNS) {
        if (pattern.test(parsedUrl.hostname)) {
          throw new Error('Private IP addresses not allowed');
        }
      }

      return parsedUrl.toString();
    } catch (error) {
      if (error instanceof TypeError) {
        throw new Error('Invalid URL format');
      }
      throw error;
    }
  }

  /**
   * 验证邮箱地址（新增方法）
   */
  static validateEmail(input: unknown): string {
    const email = this.validateString(input, {
      maxLength: 320, // RFC 5321 限制
      sanitize: true
    });

    // 企业级邮箱验证正则表达式
    const EMAIL_PATTERN = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    if (!EMAIL_PATTERN.test(email)) {
      throw new Error('Invalid email format');
    }

    // 检查本地部分和域名部分的长度
    const parts = email.split('@');
    if (parts.length !== 2) {
      throw new Error('Invalid email format');
    }

    const [localPart, domainPart] = parts;
    if (!localPart || !domainPart || localPart.length > 64 || domainPart.length > 253) {
      throw new Error('Email address components too long');
    }

    return email;
  }
}
