/**
 * MCP协议相关类型定义
 */

export interface MCPToolDefinition {
  name: string;
  description: string;
  inputSchema: {
    type: 'object';
    properties: Record<string, unknown>;
    required?: string[];
  };
}

export interface MCPToolResult {
  content: Array<
    | {
        type: 'text';
        text: string;
      }
    | {
        type: 'image';
        data: string;
        mimeType: string;
      }
    | {
        type: 'resource';
        resource: {
          uri: string;
          text?: string;
          blob?: string;
          mimeType?: string;
        };
      }
  >;
  isError?: boolean;
}

export interface MCPServerCapabilities {
  tools?: {
    listChanged?: boolean;
  };
  resources?: {
    subscribe?: boolean;
    listChanged?: boolean;
  };
  prompts?: {
    listChanged?: boolean;
  };
  logging?: Record<string, unknown>;
}

export interface MCPInitializeParams {
  protocolVersion: string;
  capabilities: {
    roots?: {
      listChanged?: boolean;
    };
    sampling?: Record<string, unknown>;
  };
  clientInfo: {
    name: string;
    version: string;
  };
}

export interface MCPInitializeResult {
  protocolVersion: string;
  capabilities: MCPServerCapabilities;
  serverInfo: {
    name: string;
    version: string;
  };
}

/**
 * 扩展的MCP服务器接口，包含tool注册方法
 */
export interface ExtendedMCPServer {
  tool(
    name: string,
    description: string,
    inputSchema: Record<string, unknown>,
    handler: () => Promise<MCPToolResult>
  ): void;
  connect(transport: unknown): Promise<void>;
}
