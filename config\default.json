{"server": {"name": "mcp-prompt-server", "version": "2.0.0", "description": "Modern MCP Prompt Server with TypeScript"}, "prompts": {"directories": ["./dist/prompts", "./src/prompts", "./custom-prompts"], "watchForChanges": false, "cacheEnabled": true, "supportedFormats": ["yaml", "json"], "maxFileSize": 1048576}, "logging": {"level": "info", "format": "json", "file": "./logs/server.log", "console": false}, "plugins": {"enabled": ["validation", "cache"], "config": {"cache": {"ttl": 3600, "maxSize": 1000}, "validation": {"strict": true, "allowExtraArgs": false}}, "autoLoad": true}, "performance": {"maxConcurrentTools": 10, "requestTimeout": 30000, "cacheSize": 1000}}