/**
 * Prompt模板相关类型定义
 */

export interface PromptTemplate {
  /** Prompt的唯一标识符 */
  name: string;
  /** Prompt功能描述 */
  description: string;
  /** 版本号 */
  version?: string;
  /** 作者信息 */
  author?: string;
  /** 标签分类 */
  tags?: string[];
  /** 参数定义 */
  arguments?: PromptArgument[];
  /** 消息内容 */
  messages: PromptMessage[];
  /** 扩展元数据 */
  metadata?: Record<string, unknown>;
  /** 缓存配置 */
  cache?: CacheConfig;
  /** 验证规则 */
  validation?: ValidationConfig;
}

export interface PromptArgument {
  /** 参数名称 */
  name: string;
  /** 参数描述 */
  description: string;
  /** 参数类型 */
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  /** 是否必需 */
  required: boolean;
  /** 默认值 */
  default?: unknown;
  /** 验证规则 */
  validation?: ValidationRule[];
  /** 参数示例 */
  examples?: unknown[];
}

export interface PromptMessage {
  /** 消息角色 */
  role: 'user' | 'assistant' | 'system';
  /** 消息内容 */
  content: {
    type: 'text' | 'image' | 'file';
    text?: string;
    image_url?: string;
    file_path?: string;
  };
  /** 条件渲染 */
  condition?: string;
}

export interface ValidationRule {
  type: 'length' | 'pattern' | 'range' | 'custom';
  value: unknown;
  message?: string;
}

export interface CacheConfig {
  enabled: boolean;
  ttl: number; // 缓存时间（秒）
  key?: string; // 自定义缓存键
}

export interface ValidationConfig {
  strict: boolean;
  allowExtraArgs: boolean;
  customValidators?: string[];
}

export interface PromptLoadResult {
  success: boolean;
  prompts: PromptTemplate[];
  errors: PromptLoadError[];
}

export interface PromptLoadError {
  file: string;
  error: string;
  line?: number;
  column?: number;
}
