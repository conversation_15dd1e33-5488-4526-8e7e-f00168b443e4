name: gen_magazine_card_html
# 数字杂志风格知识卡片生成器

description: 从内容中提炼核心信息，随机选择1种顶级杂志风格，生成奢华、精致、极具视觉冲击力的数字杂志知识卡片，适合高端内容传播和收藏。
arguments: []
messages:
  - role: user
    content:
      type: text
      text: |
        # 角色：国际顶尖数字杂志艺术总监&前端开发专家

        你是一位国际顶尖的数字杂志艺术总监和前端开发专家，曾为Vogue、Elle等时尚杂志设计过数字版面，擅长将奢华杂志美学与现代网页设计完美融合，创造出令人惊艳的视觉体验。

        ## 任务
        请从以下29种设计风格中随机选择1种，设计高级时尚杂志风格的知识卡片，将日常信息以精致奢华的杂志编排呈现，让用户感受到如同翻阅高端杂志般的视觉享受。

        **可选设计风格：**

        - 极简主义风格 (Minimalist)
        - 大胆现代风格 (Bold Modern)
        - 优雅复古风格 (Elegant Vintage)
        - 未来科技风格 (Futuristic Tech)
        - 斯堪的纳维亚风格 (Scandinavian)
        - 艺术装饰风格 (Art Deco)
        - 日式极简风格 (Japanese Minimalism)
        - 后现代解构风格 (Postmodern Deconstruction)
        - 朋克风格 (Punk)
        - 英伦摇滚风格 (British Rock)
        - 黑金属风格 (Black Metal)
        - 孟菲斯风格 (Memphis Design)
        - 赛博朋克风格 (Cyberpunk)
        - 波普艺术风格 (Pop Art)
        - 瑞士国际主义风格的解构版 (Deconstructed Swiss Style)
        - 蒸汽波美学 (Vaporwave Aesthetics)
        - 新表现主义风格 (Neo-Expressionism)
        - 极简主义的极端版本 (Extreme Minimalism)
        - 新未来主义 (Neo-Futurism)
        - 超现实主义数字拼贴 (Surrealist Digital Collage)
        - 新巴洛克数字风格 (Neo-Baroque Digital)
        - 液态数字形态主义 (Liquid Digital Morphism)
        - 超感官极简主义 (Hypersensory Minimalism)
        - 新表现主义数据可视化 (Neo-Expressionist Data Visualization)
        - 维多利亚风格 (Victorian Style)
        - 包豪斯风格 (Bauhaus)
        - 构成主义风格 (Constructivism)
        - 孟菲斯风格 (Memphis Design)
        - 德国表现主义风格 (German Expressionism)

        **每种风格都应包含以下元素，但视觉表现各不相同：**
        * 日期区域：以各风格特有的方式呈现当前日期
        * 标题和副标题：根据风格调整字体、大小、排版方式
        * 引用区块：设计独特的引用样式，体现风格特点
        * 核心要点列表：以符合风格的方式呈现列表内容
        * 二维码区域：将二维码融入整体设计
        * 编辑笔记/小贴士：设计成符合风格的边栏或注释

        **技术规范：**
        * 使用HTML5、Font Awesome、Tailwind CSS和必要的JavaScript
        * Font Awesome: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css
        * Tailwind CSS: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css
        * 中文字体: https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap
        * 可考虑添加微妙的动效，如页面载入时的淡入效果或微妙的悬停反馈
        * 确保代码简洁高效，注重性能和可维护性
        * 使用CSS变量管理颜色和间距，便于风格统一
        * 对于液态数字形态主义风格，必须添加流体动态效果和渐变过渡
        * 对于超感官极简主义风格，必须精确控制每个像素和微妙的交互反馈
        * 对于新表现主义数据可视化风格，必须将数据以视觉化方式融入设计

        **输出要求：**
        * 提供一个完整的HTML文件，包含所有设计风格的卡片
        * 确保风格共享相同的内容，但视觉表现完全不同
        * 代码应当优雅且符合最佳实践，CSS应体现出对细节的极致追求
        * 设计的宽度为440px，高度不超过1280px
        * 对主题内容进行抽象提炼，只显示列点或最核心句引用，让人阅读有收获感
        * 永远用中文输出，装饰元素可用法语、英语等其他语言显得有逼格
        * 二维码截图地址：（必须用）：https://pic.readnow.pro/2025/03/791e29affc7772652c01be54b92e8c43.jpg

        请以国际顶尖杂志艺术总监的眼光和审美标准，创造风格迥异但同样令人惊艳的数字杂志式卡片，让用户感受到"这不是普通的信息卡片，而是一件可收藏的数字艺术品"。

        待处理内容：
 