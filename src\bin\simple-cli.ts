#!/usr/bin/env node
/**
 * Production CLI Entry Point for my-prompt-mcp
 * Enterprise-grade MCP server with complete functionality
 */

/* eslint-disable no-console */

import { Command } from 'commander';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 主函数 - 处理异步初始化
async function main(): Promise<void> {
  try {
    const { TokenManager } = await import('../services/token-manager.service.js');
    const { ConfigTemplateService } = await import('../services/config-template.service.js');

    const program = new Command();
    const packageJson = JSON.parse(
      await fs.readFile(path.join(__dirname, '../../package.json'), 'utf-8')
    );

    program
      .name('my-prompt-mcp')
      .description('Production-ready MCP Prompt Server with enterprise features')
      .version(packageJson.version)
      .option('-t, --token <token>', 'Authentication token')
      .option('-c, --config <path>', 'Configuration file path')
      .option('--log-level <level>', 'Log level (debug, info, warn, error)', 'info')
      .option('--log-format <format>', 'Log format (json, text)', 'json')
      .option('--prompt-dirs <dirs>', 'Prompt directories (comma-separated)')
      .option('--production', 'Run in production mode')
      .option('--daemon', 'Run as daemon process')
      .option('--pid-file <path>', 'PID file path for daemon mode');

    // Token管理命令
    const tokenCmd = program.command('token').description('Token management commands');

    tokenCmd
      .command('create')
      .description('Create a new authentication token')
      .option('--name <name>', 'Token name', 'default-token')
      .option('--permissions <permissions>', 'Comma-separated permissions', 'user.read')
      .option('--expires <days>', 'Expiration in days (0 for never)', '0')
      .action(async options => {
        try {
          const tokenManager = new TokenManager();
          const expiresAt =
            options.expires === '0'
              ? undefined
              : new Date(Date.now() + parseInt(options.expires) * 24 * 60 * 60 * 1000);

          const token = await tokenManager.createToken({
            name: options.name,
            permissions: options.permissions.split(',').map((p: string) => p.trim()),
            expiresAt,
          });

          console.log('🎉 Token created successfully!');
          console.log(`📝 Name: ${token.name}`);
          console.log(`🔑 Token: ${token.token}`);
          console.log(`🛡️  Permissions: ${token.permissions.join(', ')}`);
          console.log(`⏰ Expires: ${token.expiresAt ? token.expiresAt.toISOString() : 'Never'}`);
          console.log(`📅 Created: ${token.createdAt.toISOString()}`);
        } catch (error) {
          console.error('❌ Failed to create token:', error);
          process.exit(1);
        }
      });

    tokenCmd
      .command('list')
      .description('List all tokens')
      .option('--show-tokens', 'Show actual token values')
      .action(async options => {
        try {
          const tokenManager = new TokenManager();
          const tokens = await tokenManager.listTokens();

          if (tokens.length === 0) {
            console.log('📭 No tokens found');
            return;
          }

          console.log(`📋 Found ${tokens.length} token(s):\n`);
          tokens.forEach((token, index) => {
            console.log(`${index + 1}. ${token.name}`);
            console.log(`   🔑 Token: ${options.showTokens ? token.token : '***HIDDEN***'}`);
            console.log(`   🛡️  Permissions: ${token.permissions.join(', ')}`);
            console.log(
              `   ⏰ Expires: ${token.expiresAt ? token.expiresAt.toISOString() : 'Never'}`
            );
            console.log(`   📅 Created: ${token.createdAt.toISOString()}`);
            console.log(
              `   🕐 Last Used: ${token.lastUsedAt ? token.lastUsedAt.toISOString() : 'Never'}`
            );
            console.log('');
          });
        } catch (error) {
          console.error('❌ Failed to list tokens:', error);
          process.exit(1);
        }
      });

    tokenCmd
      .command('verify')
      .description('Verify a token')
      .argument('<token>', 'Token to verify')
      .action(async token => {
        try {
          const tokenManager = new TokenManager();
          const result = await tokenManager.verifyToken(token);

          if (result.valid && result.token) {
            console.log('✅ Token is valid');
            console.log(`📝 Name: ${result.token.name}`);
            console.log(`🛡️  Permissions: ${result.token.permissions.join(', ')}`);
            console.log(
              `⏰ Expires: ${result.token.expiresAt ? result.token.expiresAt.toISOString() : 'Never'}`
            );
          } else {
            console.log(`❌ Token is invalid: ${result.reason || 'Unknown reason'}`);
            process.exit(1);
          }
        } catch (error) {
          console.error('❌ Failed to verify token:', error);
          process.exit(1);
        }
      });

    tokenCmd
      .command('revoke')
      .description('Revoke a token')
      .argument('<token>', 'Token to revoke')
      .option('--force', 'Force revocation without confirmation')
      .action(async (token, options) => {
        try {
          if (!options.force) {
            console.log('⚠️  This will permanently revoke the token. Continue? (y/N)');
            // In a real implementation, you'd use readline for user input
            // For now, we'll assume force mode
          }

          const tokenManager = new TokenManager();
          await tokenManager.revokeToken(token);
          console.log('✅ Token revoked successfully');
        } catch (error) {
          console.error('❌ Failed to revoke token:', error);
          process.exit(1);
        }
      });

    tokenCmd
      .command('cleanup')
      .description('Remove expired tokens')
      .action(async () => {
        try {
          const tokenManager = new TokenManager();
          const count = await tokenManager.cleanupExpiredTokens();
          console.log(`🧹 Cleaned up ${count} expired token(s)`);
        } catch (error) {
          console.error('❌ Failed to cleanup tokens:', error);
          process.exit(1);
        }
      });

    tokenCmd
      .command('stats')
      .description('Show token statistics')
      .action(async () => {
        try {
          const tokenManager = new TokenManager();
          const stats = await tokenManager.getTokenStats();

          console.log('📊 Token Statistics:\n');
          console.log(`📊 Total Tokens: ${stats.total}`);
          console.log(`✅ Active Tokens: ${stats.active}`);
          console.log(`⏰ Expired Tokens: ${stats.expired}`);
          console.log(`🚫 Revoked Tokens: ${stats.revoked}`);
        } catch (error) {
          console.error('❌ Failed to get token stats:', error);
          process.exit(1);
        }
      });

    tokenCmd
      .command('export')
      .description('Export tokens to file (for backup)')
      .argument('<file>', 'Output file path')
      .action(async file => {
        try {
          const tokenManager = new TokenManager();
          await tokenManager.exportTokens(file);
          console.log(`✅ Tokens exported to: ${file}`);
        } catch (error) {
          console.error('❌ Failed to export tokens:', error);
          process.exit(1);
        }
      });

    tokenCmd
      .command('import')
      .description('Import tokens from file')
      .argument('<file>', 'Input file path')
      .option('--merge', 'Merge with existing tokens')
      .action(async (file, options) => {
        try {
          const tokenManager = new TokenManager();
          const count = await tokenManager.importTokens(file, options.merge);
          console.log(`✅ Imported ${count} token(s) from: ${file}`);
        } catch (error) {
          console.error('❌ Failed to import tokens:', error);
          process.exit(1);
        }
      });

    // 配置管理命令
    const configCmd = program.command('config').description('Configuration management commands');

    configCmd
      .command('generate')
      .description('Generate configuration file')
      .option(
        '--template <template>',
        'Configuration template (development, production)',
        'development'
      )
      .option('--output <file>', 'Output file path', 'config.json')
      .action(async options => {
        try {
          const { LoggerService } = await import('../services/logger.service.js');
          const loggerConfig = {
            server: { name: 'cli', version: '1.0.0' },
            logging: { level: 'info' as const, format: 'text' as const, console: true },
            prompts: {
              directories: [],
              watchForChanges: false,
              cacheEnabled: false,
              supportedFormats: [],
              maxFileSize: 0,
            },
            plugins: { enabled: [], config: {}, autoLoad: false },
            performance: { maxConcurrentTools: 1, requestTimeout: 5000, cacheSize: 100 },
          };
          const logger = new LoggerService(loggerConfig);
          const configService = new ConfigTemplateService(logger);
          const template = configService.getTemplate(options.template);

          if (!template) {
            throw new Error(`Template '${options.template}' not found`);
          }

          await fs.writeFile(options.output, JSON.stringify(template.config, null, 2));
          console.log('📝 Generating configuration...');
          console.log(`✅ Configuration generated: ${options.output}`);
        } catch (error) {
          console.error('❌ Failed to generate configuration:', error);
          process.exit(1);
        }
      });

    configCmd
      .command('validate')
      .description('Validate configuration file')
      .argument('<config-file>', 'Configuration file to validate')
      .action(async configFile => {
        try {
          console.log(`🔍 Validating configuration: ${configFile}`);
          const configData = JSON.parse(await fs.readFile(configFile, 'utf-8'));

          // 基本验证 - 检查必需字段
          if (!configData.server || !configData.server.name) {
            throw new Error('Missing required field: server.name');
          }

          console.log('✅ Configuration is valid');
        } catch (error) {
          console.error('❌ Configuration validation failed:', error);
          process.exit(1);
        }
      });

    // 健康检查命令
    program
      .command('health')
      .description('Check server health')
      .option('--url <url>', 'Server URL', 'http://localhost:3000')
      .action(async options => {
        try {
          const response = await fetch(`${options.url}/health`);
          const health = (await response.json()) as { status: string; responseTime: number };

          if (response.ok) {
            console.log('✅ Server is healthy');
            console.log(`📊 Status: ${health.status}`);
            console.log(`⏱️  Response Time: ${health.responseTime}ms`);
          } else {
            console.log('❌ Server is unhealthy');
            process.exit(1);
          }
        } catch (error) {
          console.error('❌ Health check failed:', error);
          process.exit(1);
        }
      });

    // 服务器信息命令
    program
      .command('info')
      .description('Show server information')
      .action(async () => {
        try {
          console.log(`📦 Package: ${packageJson.name}`);
          console.log(`🏷️  Version: ${packageJson.version}`);
          console.log(`📄 Description: ${packageJson.description}`);
          console.log(`🟢 Node.js: ${process.version}`);
          console.log(`💻 Platform: ${process.platform}`);
          console.log(`🏗️  Architecture: ${process.arch}`);
          console.log(`📁 Working Directory: ${process.cwd()}`);
          console.log(`👤 User: ${process.env['USER'] || process.env['USERNAME'] || 'Unknown'}`);
        } catch (error) {
          console.error('❌ Failed to get server info:', error);
          process.exit(1);
        }
      });

    // 环境变量帮助
    program
      .command('env-help')
      .description('Show environment variables help')
      .action(() => {
        console.log(`
🌍 Environment Variables:

📊 Server Configuration:
  MCP_SERVER_NAME          Server name
  MCP_SERVER_DESCRIPTION   Server description  
  MCP_SERVER_PORT          Server port (default: 3000)

📝 Logging Configuration:
  MCP_LOG_LEVEL           Log level (debug, info, warn, error)
  MCP_LOG_FORMAT          Log format (json, text)
  MCP_LOG_FILE            Log file path
  MCP_LOG_CONSOLE         Enable console logging (true/false)

📚 Prompt Configuration:
  MCP_PROMPT_DIRS         Comma-separated prompt directories
  MCP_PROMPT_WATCH        Enable file watching (true/false)
  MCP_PROMPT_CACHE        Enable prompt caching (true/false)
  MCP_PROMPT_MAX_SIZE     Maximum prompt file size in bytes

⚡ Performance Configuration:
  MCP_MAX_CONCURRENT_TOOLS  Maximum concurrent tool executions
  MCP_REQUEST_TIMEOUT       Request timeout in milliseconds
  MCP_CACHE_SIZE           Cache size limit

🔌 Plugin Configuration:
  MCP_PLUGINS_ENABLED      Comma-separated enabled plugins
  MCP_PLUGINS_AUTO_LOAD    Auto-load plugins (true/false)

🔐 Authentication:
  MCP_AUTH_TOKEN          Default authentication token

📋 Example MCP Client Configuration:
{
  "mcpServers": {
    "my-prompt-mcp": {
      "command": "npx",
      "args": [
        "my-prompt-mcp",
        "--token",
        "your-token-here"
      ]
    }
  }
}

📋 Example usage:
export MCP_AUTH_TOKEN="your-token-here"
export MCP_LOG_LEVEL="info"
my-prompt-mcp --token $MCP_AUTH_TOKEN --production
        `);
      });

    await program.parseAsync(process.argv);
  } catch (error) {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  }
}

// 启动CLI
main().catch(error => {
  console.error('💥 Startup error:', error);
  process.exit(1);
});
