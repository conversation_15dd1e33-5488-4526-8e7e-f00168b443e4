name: gen_bento_grid_html
# Bento Grid风格单页网站生成器

description: 根据用户选择的设计风格和内容，生成视觉冲击力强、适合截图分享的Bento Grid单页网站，内嵌CSS和JS，优化视觉和分享体验。
arguments: []
messages:
  - role: user
    content:
      type: text
      text: |
        # 角色：极具审美的前端设计大师

        你是极具审美的前端设计大师，请为我生成一个基于 Bento Grid 设计风格的单页HTML网站，内嵌CSS、JS。这个页面将被截图分享，需要特别优化视觉效果和分享体验。

        ## 设计风格选项
        你可以选择以下风格编号或名称：
        - 极简主义风格 (Minimalist)：简约、留白、精确排版、无衬线字体、克制装饰
        - 大胆现代风格 (Bold Modern)：鲜艳对比色、不对称动态排版、极大标题、几何元素
        - 优雅复古风格 (Elegant Vintage)：米色背景、衬线字体、对称排版、精致装饰元素
        - 未来科技风格 (Futuristic Tech)：深色背景、霓虹色、科技界面、数据可视化元素
        - 斯堪的纳维亚风格 (Scandinavian)：纯白背景、北欧色调、克制排版、简单几何图案
        - 艺术装饰风格 (Art Deco)：黑金配色、对称排版、装饰性字体、几何图案、奢华感
        - 日式极简风格 (Japanese Minimalism)：极度留白、克制色彩、非对称排版、禅意美学
        - 后现代解构风格 (Postmodern Deconstruction)：打破规则、混合字体、不和谐色彩
        - 朋克风格 (Punk)：DIY效果、高对比色彩、不规则排版、手写字体、粗糙质感
        - 英伦摇滚风格 (British Rock)：英国元素、红白蓝色系、混合经典与现代字体
        - 黑金属风格 (Black Metal)：纯黑背景、哥特字体、神秘符号、高对比单色图像
        - 孟菲斯风格 (Memphis Design)：鲜艳不协调色彩、几何形状、活泼排版、80年代感
        - 赛博朋克风格 (Cyberpunk)：深色背景、霓虹色彩、故障效果、科技界面元素
        - 波普艺术风格 (Pop Art)：亮丽原色、漫画风格、半调网点效果、流行文化元素
        - 瑞士国际主义风格的解构版 (Deconstructed Swiss Style)：基于网格的破坏重组
        - 蒸汽波美学 (Vaporwave Aesthetics)：粉紫青蓝渐变、80-90年代元素、复古电脑界面
        - 新表现主义风格 (Neo-Expressionism)：强烈色彩、不规则排版、粗犷线条、手工感
        - 极简主义的极端版本 (Extreme Minimalism)：极度留白、黑白灰、精确排版、零装饰
        - 新未来主义 (Neo-Futurism)：流线型曲线、金属色调、高科技材质、动态排版
        - 超现实主义数字拼贴 (Surrealist Digital Collage)：意外元素组合、比例失调、梦幻色彩
        - 新巴洛克数字风格 (Neo-Baroque Digital)：华丽装饰、金色深色系、戏剧性光影效果
        - 液态数字形态主义 (Liquid Digital Morphism)：流体渐变、液态效果、梦幻色彩
        - 超感官极简主义 (Hypersensory Minimalism)：微妙纹理、精确排版、细微色彩变化
        - 新表现主义数据可视化 (Neo-Expressionist Data Visualization)：数据驱动的抽象艺术
        - 维多利亚风格 (Victorian Style)：华丽印刷美学、繁复装饰边框、传统排版
        - 包豪斯风格 (Bauhaus)：基本几何形状、原色、无衬线字体、功能主义美学
        - 构成主义风格 (Constructivism)：几何形状、红黑配色、动态排版、革命美学
        - 简约功能型风格 (Minimal Functional)：清晰卡片式布局、柔和色彩点缀、直观图标系统、精简文本展示、充足留白空间
        - 德国表现主义风格 (German Expressionism)：强烈明暗对比、扭曲形态、情感表达

        如果我没有指定风格，请默认使用大胆现代的 Bento Grid 风格设计。

        ## 布局要求
        - 使用不规则的网格布局，确保整个视口区域被充分利用，无明显大块空白
        - 设计一个主要的大卡片展示核心概念/引言（占据约25-30%的视觉区域）
        - 其余卡片应包含不同的子主题，每个卡片有独特的标题和简短描述，标题简短，避免换行。
        - 卡片大小应根据内容重要性进行变化，形成视觉层次感
        - 卡片之间的间距应保持一致（建议12-20px），创造整洁有序的视觉效果
        - 为卡片添加相关Fontawesome图标，出现在卡片背景中，非常巧妙的装饰。
        - 在右下角卡片放置品牌标识和二维码
        - 确保整体设计在1000px宽的视口中完整显示，无需滚动
        - 确保网格布局没有明显的"空洞"，所有区域都应有内容填充

        ## 内容分布建议
        - 主卡片：核心概念介绍（20-25%区域）
        - 4-6个中型卡片：重要子主题
        - 1个二维码卡片：位于右下角

        ## 内容展示
        - 标题使用大号字体，根据所选风格选择适合的字体，言简意赅，避免换行。
        - 正文使用易读字体，确保在所选背景上清晰可读
        - **在主大卡片展示核心理念，配色和布局大胆有冲击力，又有杂志版的精致感。**
        - 每个卡片应聚焦于单一概念，文字简洁有力，主标题加粗
        - 使用简短的要点而非长段落，便于快速阅读，如无必要，不加句子描述
        - 确保每个卡片内容量适中，避免过于空洞或过度拥挤
        - 除专业名词如Few-shot、NBA等，其他输出内容要求中文

        ## 视觉平衡
        - 确保色彩分布均匀，避免某一区域颜色过于集中，避免超过4种以上色系
        - 图标和视觉元素应均匀分布在整个布局中
        - 文本密度应相对均衡，避免某些卡片文字过多而其他过少
        - 使用视觉权重（大小、颜色、对比度）引导用户视线流动
        - 卡片形状可以变化（正方形、长方形等），但整体应保持视觉一致性

        ## 技术要求
        - 单个HTML文件，内嵌CSS
        - 使用CSS Grid实现不规则网格布局
        - 确保代码简洁，注释清晰
        - 优化页面以确保在单视口中完整显示，适合截图。实在放不下，往下方延展。
        - 使用grid-template-areas属性精确定义布局，确保无空隙

        ## 内嵌资源
        - Tailwind CSS (https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css)
        - Font Awesome (https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css)
        - 中文排版使用 Noto Serif SC 和 Noto Sans SC
        - 根据所选风格添加适合的Google Fonts字体

        ## 二维码资源
        - 使用以下URL作为二维码图片地址: https://img.t5t6.com/1746665263357-2768e6e0-83e1-475c-85ec-d20b52498bbe.jpg
        - 确保二维码尺寸足够大（至少120px），清晰可扫描，一定放一个正方形卡片显示，**不加**Fontawesome图标。
        - 在二维码周围添加简短引导文字："一起AI实战"

        ## 其他要求
        1. 不要使用任何侧边装饰线或边框强调线
        2. 卡片边框应该是完整的或完全没有，避免单侧边框装饰
        3. 视觉分隔应通过卡片背景色、间距或阴影实现，而非边框线条
        4. 如需强调，请使用背景色、字体粗细或图标，而非装饰线条
        5. 强烈推荐把Fontawesome图标作为背景装饰图案
        6. 文字和背景对比一定要清晰，可读性高
        7. 注意：不要让设计风格影响内容生成和意思传递。

        请根据我提供的主题内容和选择的风格，生成一个视觉上引人入胜、布局紧凑无空隙、配色和谐统一、适合截图分享的单页网站。

        ## 待处理内容：
        - 风格：{{style}}
        - 内容：{{content}} 