/**
 * 插件系统相关类型定义
 */

import type { PromptTemplate } from './prompt.js';
import type { ServerConfig } from './config.js';

export interface IPlugin {
  readonly name: string;
  readonly version: string;
  readonly description?: string;
  readonly dependencies?: string[];

  onLoad(_context: PluginContext): Promise<void>;
  onUnload(): Promise<void>;

  // 生命周期钩子
  onPromptLoaded?(_prompt: PromptTemplate): void;
  onToolRegistered?(_toolName: string): void;
  onToolCalled?(_toolName: string, _args: unknown): void;
  onServerStart?(): void;
  onServerStop?(): void;
  onError?(_error: Error): void;
}

export interface PluginContext {
  server: unknown; // MCP Server实例
  config: ServerConfig;
  logger: unknown;
  eventBus: unknown;
  services: {
    cache: unknown;
    config: unknown;
    watcher: unknown;
  };
}

export interface PluginMetadata {
  name: string;
  version: string;
  description?: string;
  author?: string;
  homepage?: string;
  repository?: string;
  keywords?: string[];
  dependencies?: string[];
  peerDependencies?: string[];
}

export interface PluginLoadResult {
  success: boolean;
  plugin?: IPlugin;
  error?: string;
}

export interface PluginRegistry {
  [name: string]: IPlugin;
}
