/**
 * 生产级日志服务 - 提供结构化日志、日志轮转和性能监控
 */

import winston from 'winston';
import path from 'path';
import * as fs from 'fs-extra';
import type { ServerConfig } from '../types/config.js';

export interface LogContext {
  component?: string;
  operation?: string;
  userId?: string;
  requestId?: string;
  duration?: number;
  [key: string]: unknown;
}

export interface ILogger {
  debug(message: string, context?: LogContext): void;
  info(message: string, context?: LogContext): void;
  warn(message: string, context?: LogContext): void;
  error(message: string, error?: Error, context?: LogContext): void;
  child(context: LogContext): ILogger;
  flush(): Promise<void>;
  destroy(): void;
  forComponent(component: string): ILogger;
  performance(operation: string, duration: number, context?: LogContext): void;
}

export class LoggerService implements ILogger {
  private logger: winston.Logger;
  private config: ServerConfig;
  private context: LogContext;

  constructor(config: ServerConfig, context: LogContext = {}) {
    this.config = config;
    this.context = context;
    this.logger = this.createLogger();
  }

  private createLogger(): winston.Logger {
    const transports: winston.transport[] = [];

    // 控制台输出
    if (this.config.logging.console) {
      transports.push(
        new winston.transports.Console({
          format:
            this.config.logging.format === 'json'
              ? winston.format.combine(
                  winston.format.timestamp(),
                  winston.format.errors({ stack: true }),
                  winston.format.json()
                )
              : winston.format.combine(
                  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
                  winston.format.errors({ stack: true }),
                  winston.format.printf(({ timestamp, level, message, ...meta }) => {
                    const metaStr = Object.keys(meta).length ? JSON.stringify(meta) : '';
                    return `${timestamp} [${level.toUpperCase()}] ${message} ${metaStr}`;
                  })
                ),
        })
      );
    }

    // 文件输出
    if (this.config.logging.file) {
      const logDir = path.dirname(this.config.logging.file);
      fs.ensureDirSync(logDir);

      // 主日志文件
      transports.push(
        new winston.transports.File({
          filename: this.config.logging.file,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.errors({ stack: true }),
            winston.format.json()
          ),
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
          tailable: true,
        })
      );

      // 错误日志文件
      const errorLogFile = this.config.logging.file.replace(/\.log$/, '.error.log');
      transports.push(
        new winston.transports.File({
          filename: errorLogFile,
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.errors({ stack: true }),
            winston.format.json()
          ),
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 3,
          tailable: true,
        })
      );
    }

    return winston.createLogger({
      level: this.config.logging.level,
      transports,
      exitOnError: false,
      // 添加未捕获异常处理
      exceptionHandlers: this.config.logging.file
        ? [
            new winston.transports.File({
              filename: this.config.logging.file.replace(/\.log$/, '.exceptions.log'),
            }),
          ]
        : [],
      rejectionHandlers: this.config.logging.file
        ? [
            new winston.transports.File({
              filename: this.config.logging.file.replace(/\.log$/, '.rejections.log'),
            }),
          ]
        : [],
    });
  }

  debug(message: string, context: LogContext = {}): void {
    this.logger.debug(message, { ...this.context, ...context });
  }

  info(message: string, context: LogContext = {}): void {
    this.logger.info(message, { ...this.context, ...context });
  }

  warn(message: string, context: LogContext = {}): void {
    this.logger.warn(message, { ...this.context, ...context });
  }

  error(message: string, error?: Error, context: LogContext = {}): void {
    const logContext = { ...this.context, ...context };

    if (error) {
      logContext['error'] = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        cause: error.cause,
      };
    }

    this.logger.error(message, logContext);
  }

  child(context: LogContext): ILogger {
    return new LoggerService(this.config, { ...this.context, ...context });
  }

  async flush(): Promise<void> {
    return new Promise(resolve => {
      // Winston doesn't have a direct flush method, but we can end all transports
      const transports = this.logger.transports;
      let pending = transports.length;

      if (pending === 0) {
        resolve();
        return;
      }

      transports.forEach(transport => {
        if ('flush' in transport && typeof transport.flush === 'function') {
          transport.flush(() => {
            pending--;
            if (pending === 0) resolve();
          });
        } else {
          pending--;
          if (pending === 0) resolve();
        }
      });
    });
  }

  /**
   * 记录性能指标
   */
  performance(operation: string, duration: number, context: LogContext = {}): void {
    this.info(`Performance: ${operation}`, {
      ...context,
      operation,
      duration,
      type: 'performance',
    });
  }

  /**
   * 记录安全事件
   */
  security(event: string, context: LogContext = {}): void {
    this.warn(`Security: ${event}`, {
      ...context,
      event,
      type: 'security',
    });
  }

  /**
   * 记录审计事件
   */
  audit(action: string, context: LogContext = {}): void {
    this.info(`Audit: ${action}`, {
      ...context,
      action,
      type: 'audit',
    });
  }

  /**
   * 创建请求日志器
   */
  forRequest(requestId: string): ILogger {
    return this.child({ requestId });
  }

  /**
   * 创建组件日志器
   */
  forComponent(component: string): ILogger {
    return this.child({ component });
  }

  /**
   * 获取日志统计信息
   */
  getStats(): { level: string; transports: number } {
    return {
      level: this.logger.level,
      transports: this.logger.transports.length,
    };
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.logger.close();
  }
}
