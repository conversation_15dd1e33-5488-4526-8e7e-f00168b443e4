# 环境变量配置指南

MCP Prompt Server 支持通过环境变量进行配置，这对于容器化部署和CI/CD环境特别有用。

## 🔧 支持的环境变量

### 服务器配置

| 环境变量 | 类型 | 默认值 | 描述 |
|---------|------|--------|------|
| `MCP_SERVER_NAME` | string | `mcp-prompt-server` | 服务器名称 |
| `MCP_SERVER_DESCRIPTION` | string | - | 服务器描述 |
| `MCP_SERVER_PORT` | number | - | 服务器端口（可选） |

### 日志配置

| 环境变量 | 类型 | 默认值 | 描述 |
|---------|------|--------|------|
| `MCP_LOG_LEVEL` | enum | `info` | 日志级别：`debug`, `info`, `warn`, `error` |
| `MCP_LOG_FORMAT` | enum | `json` | 日志格式：`json`, `text` |
| `MCP_LOG_FILE` | string | - | 日志文件路径 |
| `MCP_LOG_CONSOLE` | boolean | `true` | 是否输出到控制台 |

### Prompt配置

| 环境变量 | 类型 | 默认值 | 描述 |
|---------|------|--------|------|
| `MCP_PROMPT_DIRS` | string[] | `./src/prompts` | Prompt目录列表（逗号分隔） |
| `MCP_PROMPT_WATCH` | boolean | `true` | 是否监听文件变化 |
| `MCP_PROMPT_CACHE` | boolean | `true` | 是否启用缓存 |
| `MCP_PROMPT_MAX_SIZE` | number | `1048576` | 最大文件大小（字节） |

### 性能配置

| 环境变量 | 类型 | 默认值 | 描述 |
|---------|------|--------|------|
| `MCP_MAX_CONCURRENT_TOOLS` | number | `10` | 最大并发工具数 |
| `MCP_REQUEST_TIMEOUT` | number | `30000` | 请求超时时间（毫秒） |
| `MCP_CACHE_SIZE` | number | `1000` | 缓存大小 |

### 插件配置

| 环境变量 | 类型 | 默认值 | 描述 |
|---------|------|--------|------|
| `MCP_PLUGINS_ENABLED` | string[] | `validation,cache` | 启用的插件列表（逗号分隔） |
| `MCP_PLUGINS_AUTO_LOAD` | boolean | `true` | 是否自动加载插件 |

## 📝 使用示例

### 1. 基本配置

```bash
# 设置服务器名称和日志级别
export MCP_SERVER_NAME="my-prompt-server"
export MCP_LOG_LEVEL="debug"
export MCP_LOG_FORMAT="text"

# 启动服务器
npm start
```

### 2. 生产环境配置

```bash
# 生产环境配置
export MCP_SERVER_NAME="mcp-prompt-server-prod"
export MCP_LOG_LEVEL="info"
export MCP_LOG_FORMAT="json"
export MCP_LOG_FILE="./logs/production.log"
export MCP_LOG_CONSOLE="false"
export MCP_PROMPT_WATCH="false"
export MCP_MAX_CONCURRENT_TOOLS="20"
export MCP_CACHE_SIZE="5000"

npm start
```

### 3. 开发环境配置

```bash
# 开发环境配置
export MCP_SERVER_NAME="mcp-prompt-server-dev"
export MCP_LOG_LEVEL="debug"
export MCP_LOG_FORMAT="text"
export MCP_PROMPT_DIRS="./src/prompts,./dev-prompts"
export MCP_PROMPT_CACHE="false"
export MCP_REQUEST_TIMEOUT="60000"

npm run dev
```

### 4. Docker环境配置

```dockerfile
# Dockerfile
FROM node:18-alpine

# 设置环境变量
ENV MCP_SERVER_NAME="mcp-prompt-server-docker"
ENV MCP_LOG_LEVEL="info"
ENV MCP_LOG_FORMAT="json"
ENV MCP_LOG_CONSOLE="true"
ENV MCP_PROMPT_DIRS="/app/prompts"
ENV MCP_PROMPT_WATCH="false"

WORKDIR /app
COPY . .
RUN npm install && npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

### 5. Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  mcp-prompt-server:
    build: .
    environment:
      - MCP_SERVER_NAME=mcp-prompt-server-compose
      - MCP_LOG_LEVEL=info
      - MCP_LOG_FORMAT=json
      - MCP_LOG_FILE=/app/logs/server.log
      - MCP_PROMPT_DIRS=/app/prompts,/app/custom-prompts
      - MCP_PROMPT_CACHE=true
      - MCP_MAX_CONCURRENT_TOOLS=15
      - MCP_CACHE_SIZE=2000
    volumes:
      - ./prompts:/app/prompts
      - ./custom-prompts:/app/custom-prompts
      - ./logs:/app/logs
    ports:
      - "3000:3000"
```

### 6. Kubernetes配置

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-prompt-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mcp-prompt-server
  template:
    metadata:
      labels:
        app: mcp-prompt-server
    spec:
      containers:
      - name: mcp-prompt-server
        image: mcp-prompt-server:latest
        env:
        - name: MCP_SERVER_NAME
          value: "mcp-prompt-server-k8s"
        - name: MCP_LOG_LEVEL
          value: "info"
        - name: MCP_LOG_FORMAT
          value: "json"
        - name: MCP_LOG_CONSOLE
          value: "true"
        - name: MCP_PROMPT_DIRS
          value: "/app/prompts"
        - name: MCP_PROMPT_WATCH
          value: "false"
        - name: MCP_MAX_CONCURRENT_TOOLS
          value: "10"
        - name: MCP_CACHE_SIZE
          value: "1000"
        volumeMounts:
        - name: prompts-volume
          mountPath: /app/prompts
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: prompts-volume
        configMap:
          name: prompts-config
      - name: logs-volume
        emptyDir: {}
```

## 🔒 安全注意事项

### 1. 敏感信息处理

```bash
# 不要在环境变量中存储敏感信息
# 错误示例：
export MCP_DATABASE_PASSWORD="secret123"

# 正确做法：使用配置文件加密或密钥管理服务
export MCP_CONFIG_FILE="/secure/config.encrypted"
export MCP_CONFIG_PASSWORD_FILE="/secure/password"
```

### 2. 环境变量验证

```bash
# 启动前验证关键环境变量
if [ -z "$MCP_SERVER_NAME" ]; then
  echo "Error: MCP_SERVER_NAME is required"
  exit 1
fi

if [ -z "$MCP_LOG_LEVEL" ]; then
  echo "Warning: MCP_LOG_LEVEL not set, using default 'info'"
fi
```

## 🚀 最佳实践

### 1. 环境分离

```bash
# 使用不同的环境变量文件
# .env.development
MCP_LOG_LEVEL=debug
MCP_LOG_FORMAT=text
MCP_PROMPT_CACHE=false

# .env.production
MCP_LOG_LEVEL=info
MCP_LOG_FORMAT=json
MCP_PROMPT_CACHE=true
```

### 2. 配置验证脚本

```bash
#!/bin/bash
# validate-config.sh

echo "Validating MCP configuration..."

# 检查必需的环境变量
required_vars=("MCP_SERVER_NAME")
for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    echo "Error: $var is required but not set"
    exit 1
  fi
done

# 验证数值类型
if [ -n "$MCP_SERVER_PORT" ] && ! [[ "$MCP_SERVER_PORT" =~ ^[0-9]+$ ]]; then
  echo "Error: MCP_SERVER_PORT must be a number"
  exit 1
fi

echo "Configuration validation passed!"
```

### 3. 动态配置更新

```bash
# 使用信号重新加载配置
kill -HUP $(pgrep -f "mcp-prompt-server")

# 或者使用管理API
curl -X POST http://localhost:3000/admin/reload-config
```

## 📊 配置优先级

配置的优先级从高到低：

1. **环境变量** - 最高优先级
2. **配置文件** - 中等优先级  
3. **默认配置** - 最低优先级

```bash
# 示例：即使配置文件中设置了 log_level: "info"
# 环境变量会覆盖它
export MCP_LOG_LEVEL="debug"  # 这个会生效
```

## 🔍 故障排除

### 1. 查看当前配置

```bash
# 启动时会显示加载的配置
npm start

# 或者使用管理工具
npm run config:show
```

### 2. 配置验证

```bash
# 验证配置是否正确
npm run config:validate

# 显示配置来源
npm run config:debug
```

### 3. 常见问题

**问题：环境变量不生效**
```bash
# 确保环境变量已正确设置
echo $MCP_LOG_LEVEL

# 检查变量名是否正确（区分大小写）
env | grep MCP_
```

**问题：布尔值配置错误**
```bash
# 布尔值必须是字符串 "true" 或 "false"
export MCP_PROMPT_CACHE="true"   # 正确
export MCP_PROMPT_CACHE=true     # 错误
```
