/**
 * 工具注册器 - 负责将Prompt模板转换为MCP工具并管理工具生命周期
 */

import { z } from 'zod';
import { EventEmitter } from 'eventemitter3';
import type { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import type { PromptTemplate, PromptArgument } from '../types/prompt.js';
import type { MCPToolResult } from '../types/mcp.js';
import type { ServerConfig } from '../types/config.js';

export interface IToolRegistry extends EventEmitter {
  registerTool(prompt: PromptTemplate): void;
  unregisterTool(name: string): void;
  getRegisteredTools(): string[];
  hasToolRegistered(name: string): boolean;
  clearAllTools(): void;
  destroy(): void;
}

export class ToolRegistry extends EventEmitter implements IToolRegistry {
  private server: McpServer;
  private registeredTools = new Set<string>();

  constructor(server: McpServer, _config: ServerConfig) {
    super();
    this.server = server;
  }

  /**
   * 将Prompt模板注册为MCP工具
   */
  registerTool(prompt: PromptTemplate): void {
    try {
      // 如果工具已存在，先注销
      if (this.registeredTools.has(prompt.name)) {
        this.unregisterTool(prompt.name);
      }

      // 构建工具的输入schema
      const inputSchema = this.buildInputSchema(prompt);

      // 注册工具 - 使用正确的类型断言
      (
        this.server as unknown as {
          tool: (
            name: string,
            description: string,
            schema: Record<string, unknown>,
            handler: (args: Record<string, unknown>) => Promise<unknown>
          ) => void;
        }
      ).tool(
        prompt.name,
        prompt.description || `Prompt: ${prompt.name}`,
        inputSchema,
        async (args: Record<string, unknown>) => {
          return this.handleToolCall(prompt, args);
        }
      );

      this.registeredTools.add(prompt.name);
      this.emit('tool:registered', prompt.name, prompt);
    } catch (error) {
      this.emit('tool:error', prompt.name, error);
      throw new Error(
        `Failed to register tool "${prompt.name}": ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * 构建工具的输入schema
   */
  private buildInputSchema(prompt: PromptTemplate): Record<string, z.ZodType> {
    const schemaObj: Record<string, z.ZodType> = {};

    if (prompt.arguments && Array.isArray(prompt.arguments)) {
      for (const arg of prompt.arguments) {
        const zodType = this.createZodType(arg);
        schemaObj[arg.name] = zodType;
      }
    }

    return schemaObj;
  }

  /**
   * 根据参数定义创建Zod类型
   */
  private createZodType(arg: PromptArgument): z.ZodType {
    let zodType: z.ZodType;

    switch (arg.type) {
      case 'string':
        zodType = z.string();
        break;
      case 'number':
        zodType = z.number();
        break;
      case 'boolean':
        zodType = z.boolean();
        break;
      case 'array':
        zodType = z.array(z.unknown());
        break;
      case 'object':
        zodType = z.record(z.unknown());
        break;
      default:
        zodType = z.string();
    }

    // 添加描述
    if (arg.description) {
      zodType = zodType.describe(arg.description);
    }

    // 处理可选参数
    if (!arg.required) {
      zodType = zodType.optional();

      // 添加默认值
      if (arg.default !== undefined) {
        zodType = zodType.default(arg.default);
      }
    }

    // 添加验证规则
    if (arg.validation && arg.validation.length > 0) {
      zodType = this.applyValidationRules(zodType, arg.validation);
    }

    return zodType;
  }

  /**
   * 应用验证规则
   */
  private applyValidationRules(zodType: z.ZodType, rules: PromptArgument['validation']): z.ZodType {
    if (!rules) return zodType;

    for (const rule of rules) {
      switch (rule.type) {
        case 'length':
          if (zodType instanceof z.ZodString) {
            const lengthValue = rule.value as number | { min?: number; max?: number };
            if (typeof lengthValue === 'number') {
              zodType = (zodType as z.ZodString).length(lengthValue);
            } else {
              if (lengthValue.min !== undefined) {
                zodType = (zodType as z.ZodString).min(lengthValue.min);
              }
              if (lengthValue.max !== undefined) {
                zodType = (zodType as z.ZodString).max(lengthValue.max);
              }
            }
          }
          break;
        case 'pattern':
          if (zodType instanceof z.ZodString) {
            zodType = zodType.regex(new RegExp(rule.value as string));
          }
          break;
        case 'range':
          if (zodType instanceof z.ZodNumber) {
            const rangeValue = rule.value as { min?: number; max?: number };
            if (rangeValue.min !== undefined) {
              zodType = (zodType as z.ZodNumber).min(rangeValue.min);
            }
            if (rangeValue.max !== undefined) {
              zodType = (zodType as z.ZodNumber).max(rangeValue.max);
            }
          }
          break;
        case 'custom':
          // 自定义验证规则可以在这里实现
          break;
      }
    }

    return zodType;
  }

  /**
   * 处理工具调用
   */
  private async handleToolCall(
    prompt: PromptTemplate,
    args: Record<string, unknown>
  ): Promise<MCPToolResult> {
    try {
      this.emit('tool:called', prompt.name, args);

      // 处理prompt内容
      const processedContent = await this.processPromptContent(prompt, args);

      const result: MCPToolResult = {
        content: [
          {
            type: 'text',
            text: processedContent,
          },
        ],
      };

      this.emit('tool:success', prompt.name, args, result);
      return result;
    } catch (error) {
      this.emit('tool:error', prompt.name, args, error);

      return {
        content: [
          {
            type: 'text',
            text: `Error processing prompt "${prompt.name}": ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }

  /**
   * 处理Prompt内容，替换参数占位符
   */
  private async processPromptContent(
    prompt: PromptTemplate,
    args: Record<string, unknown>
  ): Promise<string> {
    let promptText = '';

    if (prompt.messages && Array.isArray(prompt.messages)) {
      // 只处理用户消息
      const userMessages = prompt.messages.filter(msg => msg.role === 'user');

      for (const message of userMessages) {
        if (message.content && typeof message.content.text === 'string') {
          let text = message.content.text;

          // 替换所有 {{arg}} 格式的参数
          for (const [key, value] of Object.entries(args)) {
            const placeholder = new RegExp(`{{${key}}}`, 'g');
            text = text.replace(placeholder, String(value));
          }

          // 检查条件渲染
          if (message.condition) {
            const shouldRender = await this.evaluateCondition(message.condition, args);
            if (!shouldRender) {
              continue;
            }
          }

          promptText += `${text}\n\n`;
        }
      }
    }

    return promptText.trim();
  }

  /**
   * 评估条件表达式 - 使用安全的表达式评估器
   */
  private async evaluateCondition(
    condition: string,
    args: Record<string, unknown>
  ): Promise<boolean> {
    // 使用安全的表达式评估器替代eval()
    const { SafeExpressionEvaluator } = await import('../utils/expression-evaluator.js');
    return SafeExpressionEvaluator.evaluateCondition(condition, args);
  }

  /**
   * 注销工具
   */
  unregisterTool(name: string): void {
    if (this.registeredTools.has(name)) {
      // MCP SDK 可能没有直接的注销方法，这里记录状态
      this.registeredTools.delete(name);
      this.emit('tool:unregistered', name);
    }
  }

  /**
   * 获取已注册的工具列表
   */
  getRegisteredTools(): string[] {
    return Array.from(this.registeredTools);
  }

  /**
   * 检查工具是否已注册
   */
  hasToolRegistered(name: string): boolean {
    return this.registeredTools.has(name);
  }

  /**
   * 清除所有工具
   */
  clearAllTools(): void {
    const tools = Array.from(this.registeredTools);
    for (const tool of tools) {
      this.unregisterTool(tool);
    }
    this.emit('tools:cleared');
  }

  /**
   * 获取工具统计信息
   */
  getStats(): { totalTools: number; registeredTools: string[] } {
    return {
      totalTools: this.registeredTools.size,
      registeredTools: this.getRegisteredTools(),
    };
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.clearAllTools();
    this.removeAllListeners();
  }
}
