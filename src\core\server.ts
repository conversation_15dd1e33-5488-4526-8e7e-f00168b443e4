/**
 * MCP服务器核心类 - 协调所有组件，提供统一的服务器接口
 * 完整的生产级MCP服务器实现，支持prompts和tools端点
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { EventEmitter } from 'eventemitter3';
import type { ServerConfig } from '../types/config.js';
import type { PromptTemplate } from '../types/prompt.js';
import type { ServerEvents } from '../types/events.js';

import { PromptLoader, type IPromptLoader } from './prompt-loader.js';
import { ToolRegistry, type IToolRegistry } from './tool-registry.js';
import { LoggerService, type ILogger } from '../services/logger.service.js';
import { MetricsService, type IMetricsService } from '../services/metrics.service.js';
import { ResourceLimiterService } from '../services/resource-limiter.service.js';

export interface IMCPPromptServer {
  start(): Promise<void>;
  stop(): Promise<void>;
  reload(): Promise<void>;
  getStats(): ServerStats;
}

export interface ServerStats {
  uptime: number;
  totalPrompts: number;
  totalTools: number;
  loadedPrompts: string[];
  registeredTools: string[];
}

export class MCPPromptServer extends EventEmitter<ServerEvents> implements IMCPPromptServer {
  private mcpServer: McpServer;
  private transport: StdioServerTransport;
  private promptLoader: IPromptLoader;
  private toolRegistry: IToolRegistry;
  private logger: ILogger;
  private metrics: IMetricsService;
  private resourceLimiter: ResourceLimiterService;
  private config: ServerConfig;
  private startTime: number = 0;
  private isRunning = false;

  constructor(config: ServerConfig) {
    super();
    this.config = config;

    // 初始化日志服务
    this.logger = new LoggerService(config);

    // 初始化性能监控
    this.metrics = new MetricsService(this.logger);

    // 初始化资源限制器
    this.resourceLimiter = new ResourceLimiterService(config, this.logger);

    // 初始化MCP服务器
    this.mcpServer = new McpServer({
      name: config.server.name,
      version: config.server.version,
    });

    // 初始化传输层
    this.transport = new StdioServerTransport();

    // 初始化组件
    this.promptLoader = new PromptLoader(config);
    this.toolRegistry = new ToolRegistry(this.mcpServer, config);

    // 设置MCP处理器
    this.setupMCPHandlers();

    // 设置事件监听
    this.setupEventListeners();
  }

  /**
   * 设置MCP处理器 - 完整的生产级实现
   * 注意：在MCP协议中，prompts通常作为tools实现，而不是独立的端点
   */
  private setupMCPHandlers(): void {
    // MCP服务器会自动处理以下标准端点：
    // - initialize: 由MCP SDK自动处理
    // - tools/list: 由ToolRegistry通过.tool()方法自动注册
    // - tools/call: 由ToolRegistry通过.tool()方法自动处理

    // 设置服务器能力
    this.setupServerCapabilities();
  }

  /**
   * 设置服务器能力
   */
  private setupServerCapabilities(): void {
    // 服务器能力由MCP SDK自动管理
    // 这里可以添加任何额外的初始化逻辑
    this.logger.info('MCP server capabilities configured', {
      toolsSupported: true,
      promptsAsTools: true,
      resourcesSupported: false,
      loggingSupported: true,
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // Prompt加载器事件
    this.promptLoader.on('prompt:loaded', (prompt: PromptTemplate) => {
      this.emit('prompt:loaded', prompt);
      // 自动注册为工具
      this.toolRegistry.registerTool(prompt);
    });

    this.promptLoader.on('prompts:loaded', (prompts: PromptTemplate[]) => {
      this.emit('prompts:loaded', prompts);
    });

    this.promptLoader.on('prompts:reloaded', (prompts: PromptTemplate[]) => {
      this.emit('prompts:reloaded', prompts);
    });

    this.promptLoader.on('error', (error: Error) => {
      this.emit('server:error', error);
    });

    // 工具注册器事件
    this.toolRegistry.on('tool:registered', (name: string) => {
      this.emit('tool:registered', name, undefined);
    });

    this.toolRegistry.on('tool:called', (name: string, args: unknown, result: unknown) => {
      this.emit('tool:called', name, args, result);
    });

    this.toolRegistry.on('tool:error', (name: string, args: unknown, error: Error) => {
      this.emit('tool:error', name, args, error);
    });
  }

  /**
   * 启动服务器
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      throw new Error('Server is already running');
    }

    try {
      this.startTime = Date.now();
      this.logger.info('Starting MCP Prompt Server', {
        version: this.config.server.version,
        name: this.config.server.name,
      });

      // 启动性能监控
      this.metrics.startCollection();

      // 加载Prompt模板
      const loadResult = await this.resourceLimiter.executeWithLimits(
        () => this.promptLoader.loadPrompts(this.config.prompts.directories),
        'load_prompts'
      );

      if (!loadResult.success) {
        this.logger.warn('Some prompts failed to load', {
          errorCount: loadResult.errors.length,
          errors: loadResult.errors,
        });
      }

      // 记录加载的Prompt数量
      this.metrics.recordMetric('prompts_loaded', loadResult.prompts.length);
      this.metrics.incrementCounter('prompts_loaded');

      // 注册管理工具
      this.registerManagementTools();

      // 启动文件监听（如果启用）
      if (this.config.prompts.watchForChanges) {
        this.promptLoader.watchForChanges(prompts => {
          this.handlePromptsReload(prompts);
        });
      }

      // 连接MCP传输层
      await this.mcpServer.connect(this.transport);

      this.isRunning = true;
      this.emit('server:started');

      this.logger.info('MCP Prompt Server started successfully', {
        promptsLoaded: loadResult.prompts.length,
        toolsRegistered: this.toolRegistry.getRegisteredTools().length,
        uptime: Date.now() - this.startTime,
      });
    } catch (error) {
      this.logger.error('Failed to start server', error as Error);
      this.emit('server:error', error as Error);
      throw error;
    }
  }

  /**
   * 停止服务器
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      this.logger.info('Stopping MCP Prompt Server');

      // 停止性能监控
      this.metrics.stopCollection();

      // 停止文件监听
      this.promptLoader.stopWatching();

      // 清理工具注册
      this.toolRegistry.clearAllTools();

      // 刷新日志
      await this.logger.flush();

      this.isRunning = false;
      this.emit('server:stopped');

      this.logger.info('MCP Prompt Server stopped gracefully');
    } catch (error) {
      this.logger.error('Error during server shutdown', error as Error);
      this.emit('server:error', error as Error);
      throw error;
    }
  }

  /**
   * 重新加载Prompt模板
   */
  async reload(): Promise<void> {
    try {
      // 清除现有工具
      this.toolRegistry.clearAllTools();

      // 重新加载Prompt
      const loadResult = await this.promptLoader.reloadPrompts();

      if (!loadResult.success) {
        this.logger.warn('Some prompts failed to reload', { errors: loadResult.errors });
      }

      this.logger.info(`Reloaded ${loadResult.prompts.length} prompts`);
    } catch (error) {
      this.emit('server:error', error as Error);
      throw error;
    }
  }

  /**
   * 处理Prompt重新加载
   */
  private handlePromptsReload(prompts: PromptTemplate[]): void {
    // 清除现有工具
    this.toolRegistry.clearAllTools();

    // 重新注册工具
    for (const prompt of prompts) {
      this.toolRegistry.registerTool(prompt);
    }

    this.logger.info(`Hot reloaded ${prompts.length} prompts`);
  }

  /**
   * 注册管理工具
   */
  private registerManagementTools(): void {
    // 重新加载prompts工具
    (
      this.mcpServer as unknown as {
        tool: (
          name: string,
          description: string,
          schema: Record<string, unknown>,
          handler: () => Promise<unknown>
        ) => void;
      }
    ).tool('reload_prompts', '重新加载所有预设的prompts', {}, async () => {
      await this.reload();
      const stats = this.getStats();
      return {
        content: [
          {
            type: 'text',
            text: `成功重新加载了 ${stats.totalPrompts} 个prompts，注册了 ${stats.totalTools} 个工具。`,
          },
        ],
      };
    });

    // 获取prompt名称列表工具
    (
      this.mcpServer as unknown as {
        tool: (
          name: string,
          description: string,
          schema: Record<string, unknown>,
          handler: () => Promise<unknown>
        ) => void;
      }
    ).tool('get_prompt_names', '获取所有可用的prompt名称', {}, async () => {
      const stats = this.getStats();
      return {
        content: [
          {
            type: 'text',
            text: `可用的prompts (${stats.totalPrompts}):\n${stats.loadedPrompts.join('\n')}`,
          },
        ],
      };
    });

    // 获取服务器状态工具
    (
      this.mcpServer as unknown as {
        tool: (
          name: string,
          description: string,
          schema: Record<string, unknown>,
          handler: () => Promise<unknown>
        ) => void;
      }
    ).tool('get_server_status', '获取服务器运行状态和统计信息', {}, async () => {
      const stats = this.getStats();
      const status = {
        running: this.isRunning,
        uptime: `${Math.floor(stats.uptime / 1000)}s`,
        prompts: stats.totalPrompts,
        tools: stats.totalTools,
        config: {
          watchForChanges: this.config.prompts.watchForChanges,
          cacheEnabled: this.config.prompts.cacheEnabled,
          directories: this.config.prompts.directories,
        },
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(status, null, 2),
          },
        ],
      };
    });
  }

  /**
   * 获取服务器统计信息
   */
  getStats(): ServerStats {
    const loadedPrompts = this.promptLoader.getLoadedPrompts();
    const registeredTools = this.toolRegistry.getRegisteredTools();

    return {
      uptime: this.startTime > 0 ? Date.now() - this.startTime : 0,
      totalPrompts: loadedPrompts.length,
      totalTools: registeredTools.length,
      loadedPrompts: loadedPrompts.map(p => p.name),
      registeredTools,
    };
  }

  /**
   * 获取配置信息
   */
  getConfig(): ServerConfig {
    return { ...this.config };
  }

  /**
   * 清理资源
   */
  destroy(): void {
    if (this.isRunning) {
      this.stop().catch(error => {
        this.logger.error('Error during shutdown', error);
      });
    }

    // 清理所有服务
    this.promptLoader.destroy();
    this.toolRegistry.destroy();
    this.metrics.destroy();
    this.resourceLimiter.destroy();
    this.logger.destroy();

    this.removeAllListeners();
  }
}
