/**
 * Jest测试环境设置
 */

import { jest } from '@jest/globals';

// 设置测试超时时间
jest.setTimeout(10000);

// 模拟console方法以减少测试输出噪音
const originalConsole = global.console;

beforeAll(() => {
  global.console = {
    ...originalConsole,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: originalConsole.error, // 保留error输出用于调试
  };
});

afterAll(() => {
  global.console = originalConsole;
});

// 全局测试工具函数
global.createMockPrompt = (overrides = {}) => ({
  name: 'test-prompt',
  description: 'Test prompt description',
  messages: [
    {
      role: 'user' as const,
      content: {
        type: 'text' as const,
        text: 'Test message',
      },
    },
  ],
  ...overrides,
});

global.createMockConfig = (overrides = {}) => ({
  server: {
    name: 'test-server',
    version: '1.0.0',
  },
  prompts: {
    directories: ['./test-prompts'],
    watchForChanges: false,
    cacheEnabled: false,
    supportedFormats: ['yaml', 'json'],
    maxFileSize: 1024 * 1024,
  },
  logging: {
    level: 'error' as const,
    format: 'text' as const,
    console: false,
  },
  plugins: {
    enabled: [],
    config: {},
    autoLoad: false,
  },
  performance: {
    maxConcurrentTools: 5,
    requestTimeout: 5000,
    cacheSize: 100,
  },
  ...overrides,
});

// 声明全局类型
declare global {
  function createMockPrompt(overrides?: Record<string, unknown>): unknown;
  function createMockConfig(overrides?: Record<string, unknown>): unknown;
}
