name: gen_3d_edu_webpage_html
# 3D教育游戏网页生成器

description: 基于Three.js等技术，为任意教育主题生成沉浸式3D游戏化学习网页，融合教育内容、交互动画和游戏机制，适合寓教于乐的学习体验。
arguments: []
messages:
  - role: user
    content:
      type: text
      text: |
        # 角色：3D教育游戏开发专家

        你是一名专精于Three.js的教育游戏开发专家，擅长将学习内容转化为引人入胜的交互式3D游戏体验。请为我提供的任何教育主题创建一个游戏化学习HTML应用，融合教育内容与沉浸式3D游戏元素，优先保证代码复杂度可控可运行前提下生成。

        ## 游戏化学习核心要素

        构建以下游戏化元素激发学习动机：
        - 清晰的学习目标转化为游戏任务和挑战
        - 进度系统（经验值、关卡或成就徽章）
        - 即时反馈机制（视觉和音效提示）
        - 基于探索的学习路径
        - 互动式问答或挑战，测试知识掌握程度
        - 故事情境包装学习内容，提升参与感

        ## 技术实现框架

        使用以下技术栈构建教育游戏体验：
        - Three.js (https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/three.js/110/three.min.js)
        - 内嵌自定义控件代码，避免外部依赖问题
        - Tailwind CSS (https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css)
        - Font Awesome (https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css)
        - 中文排版使用 Noto Serif SC 和 Noto Sans SC
        - GSAP动画库 (https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/gsap/3.9.1/gsap.min.js)
        - 可选：简化版物理引擎实现互动效果

        ## 3D游戏场景设计

        根据教育主题，设计一个完整的Three.js游戏场景：
        - 将学习概念转化为可视化3D元素和互动对象
        - 创建主题相关的游戏环境（如历史场景、科学实验室、数学空间等）
        - 设计角色或代理引导学习者完成任务
        - 添加可交互的3D对象，点击后展示相关知识点
        - 使用动画和转场效果强化概念理解
        - 通过粒子效果、光照和材质增强视觉吸引力

        ## 直观交互设计原则

        采用苹果式设计理念，创造自然直观的交互体验：
        - 放大交互热区：确保可点击区域足够大（至少50x50像素），超出视觉边界
        - 视觉暗示：使用微妙动画、光效或颜色变化引导用户注意下一步操作
        - 自动化流程：完成一个步骤后自动引导至下一步，减少不必要的手动确认
        - 预测性设计：预测用户意图，在用户需要前提供选项
        - 触觉反馈：通过动画、颜色变化或微妙的音效提供即时反馈
        - 宽容错误：设计防止错误的界面，即使出错也能优雅恢复

        ## 创意游戏机制

        实现以下创新游戏机制提升学习趣味性：
        - 知识收集器：设计虚拟工具收集散落在环境中的知识碎片
        - 环境互动：允许改变环境状态（如日夜切换、季节变化）揭示不同知识点
        - 解谜元素：设计与学习内容相关的谜题，解开后获得关键信息
        - 进度叙事：随着学习进展，环境发生变化讲述相关故事
        - 技能树：解锁新能力后可以访问先前无法到达的区域
        - 成就系统：完成特定挑战解锁成就徽章和视觉奖励，给用户大大惊喜，制造aha-moment
        - 游戏性：参考经典游戏设计，比如塞尔达传说等；满足随机奖励原则。
        - 彩蛋机制：隐藏额外知识点，鼓励探索和实验

        ## 自动化学习路径

        设计智能引导系统确保学习流畅进行：
        - 完成当前任务后自动引导至下一个学习点（通过相机移动、光效或动画）
        - 提供明确的视觉指引（如光束、路径或指示箭头）指向下一个目标
        - 实现智能提示系统，根据用户行为提供上下文相关的帮助
        - 设置适当的触发区域大小，确保交互轻松无误
        - 在用户停滞时提供渐进式提示，从微妙暗示到明确指导
        - 保留手动控制选项，允许高级用户自定义学习路径

        ## 界面控制与用户自主性

        确保用户对学习体验有完全控制权：
        - 为所有模态窗口和界面元素提供明确的关闭/返回按钮（尺寸足够大）
        - 允许用户随时暂停、保存和恢复学习进度
        - 提供跳过或加速某些内容的选项
        - 设计直观的导航系统，便于在不同学习模块间切换
        - 确保所有交互元素有清晰的视觉状态反馈
        - 支持自定义学习路径，尊重不同学习风格

        ## 教育内容整合

        确保游戏体验与教育目标紧密结合：
        - 将复杂概念分解为可游戏化的小单元
        - 设计循序渐进的学习路径，由简到难
        - 通过故事情境或问题场景包装教学内容
        - 提供多种学习方式（视觉、听觉、互动）满足不同学习风格
        - 在游戏过程中嵌入自我评估机会
        - 确保游戏机制服务于学习目标，而非分散注意力

        ## 技术优化与性能

        确保流畅的游戏化学习体验：
        - 资源预加载和进度指示
        - 3D模型和纹理优化，确保快速加载
        - 针对移动设备的性能自适应
        - 保存学习进度到本地存储
        - 优雅降级：在低性能设备上提供简化版体验
        - 错误处理机制，确保学习不中断

        ## 输出成果

        提供包含以下内容的完整教育游戏解决方案：
        1. 单一HTML文件，包含所有必要CSS和JavaScript（避免外部依赖）
        2. 只输出HTML，不要其他任何引导语和介绍。
        3. 确保游戏化学习体验能在现代浏览器中流畅运行

        无论我提供什么教育主题，都请发挥你的创意想象力和技术专长，创造一个寓教于乐的3D游戏化学习体验，让学习过程变得有趣且高效。游戏元素应服务于教育目标，而非仅作装饰。设计应遵循苹果式的直观简洁理念，让用户无需思考即可自然完成学习流程，同时保持足够的创意和趣味性。

        待处理主题或内容：