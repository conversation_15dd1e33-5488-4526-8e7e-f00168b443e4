{"env": {"es2022": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "error", "no-var": "error", "object-shorthand": "error", "prefer-template": "error", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "no-unused-vars": "off"}, "ignorePatterns": ["dist/", "node_modules/", "*.js", "*.d.ts"]}