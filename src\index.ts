/**
 * MCP Prompt Server 入口文件
 * 支持stdio传输的MCP服务器实现
 */

import { ConfigService } from './services/config.service.js';
import { MCPPromptServer } from './core/server.js';

/**
 * 主启动函数
 */
async function main(): Promise<void> {
  try {
    // 初始化配置服务
    const configService = new ConfigService();
    const config = configService.getAll();

    // 确保日志不输出到stdout（MCP使用stdio通信）
    config.logging.console = false;

    // 创建服务器实例
    const server = new MCPPromptServer(config);

    // 设置事件监听（使用stderr避免干扰stdio）
    setupEventListeners(server);

    // 启动服务器
    await server.start();

    // 设置优雅关闭
    setupGracefulShutdown(server, configService);
  } catch (error) {
    // 使用stderr输出错误，避免干扰MCP通信
    console.error('Failed to start MCP server:', error);
    process.exit(1);
  }
}

/**
 * 设置事件监听器（使用stderr避免干扰MCP stdio通信）
 */
function setupEventListeners(server: MCPPromptServer): void {
  // 服务器事件 - 使用stderr输出
  server.on('server:started', () => {
    process.stderr.write('MCP Prompt Server started\n');
  });

  server.on('server:stopped', () => {
    process.stderr.write('MCP Prompt Server stopped\n');
  });

  server.on('server:error', (error: Error) => {
    process.stderr.write(`Server error: ${error.message}\n`);
  });

  // 其他事件静默处理，避免干扰stdio
  server.on('prompt:loaded', () => {
    // 静默处理
  });

  server.on('prompts:loaded', () => {
    // 静默处理
  });

  server.on('prompts:reloaded', () => {
    // 静默处理
  });

  server.on('tool:registered', () => {
    // 静默处理
  });

  server.on('tool:called', () => {
    // 静默处理
  });

  server.on('tool:error', (name, _args, error) => {
    process.stderr.write(`Tool error in ${name}: ${error.message}\n`);
  });
}

/**
 * 设置优雅关闭
 */
function setupGracefulShutdown(server: MCPPromptServer, configService: ConfigService): void {
  const shutdown = async (signal: string) => {
    process.stderr.write(`Received ${signal}, shutting down...\n`);

    try {
      // 停止服务器
      await server.stop();

      // 清理配置服务
      configService.destroy();

      process.exit(0);
    } catch (error) {
      process.stderr.write(`Error during shutdown: ${error}\n`);
      process.exit(1);
    }
  };

  // 监听关闭信号
  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));

  // 处理未捕获的异常
  process.on('uncaughtException', error => {
    process.stderr.write(`Uncaught Exception: ${error}\n`);
    shutdown('uncaughtException').catch(() => process.exit(1));
  });

  process.on('unhandledRejection', reason => {
    process.stderr.write(`Unhandled Rejection: ${reason}\n`);
    shutdown('unhandledRejection').catch(() => process.exit(1));
  });
}

// 启动应用
main().catch(error => {
  process.stderr.write(`Fatal error: ${error}\n`);
  process.exit(1);
});
