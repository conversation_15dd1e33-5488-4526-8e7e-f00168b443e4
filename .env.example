# Environment Configuration for my-prompt-mcp
# Copy this file to .env and update the values

# Authentication
MCP_AUTH_TOKEN=t1AbqYHRe5hDXmNV7t0J4

# Logging Configuration
MCP_LOG_LEVEL=info
MCP_LOG_FORMAT=json
MCP_LOG_FILE=/app/logs/server.log

# Server Configuration
MCP_SERVER_NAME=my-prompt-mcp-production
MCP_PROMPT_DIRS=/app/prompts,/app/custom-prompts

# Performance Settings
MCP_MAX_CONCURRENT_TOOLS=20
MCP_REQUEST_TIMEOUT=30000
MCP_CACHE_SIZE=5000

# Monitoring (optional)
GRAFANA_PASSWORD=admin123
PROMETHEUS_RETENTION=200h

# Development Settings (for development profile)
MCP_DEV_LOG_LEVEL=debug
MCP_DEV_LOG_FORMAT=text
MCP_DEV_WATCH_CHANGES=true
