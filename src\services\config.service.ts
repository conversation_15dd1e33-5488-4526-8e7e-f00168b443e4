/**
 * 配置管理服务 - 提供统一的配置管理接口
 */

import { EventEmitter } from 'eventemitter3';
import { z } from 'zod';
import { existsSync, readFileSync, watch, FSWatcher } from 'fs';
import path from 'path';
import type { ServerConfig } from '../types/config.js';

// 配置验证Schema
const ServerConfigSchema = z.object({
  server: z.object({
    name: z.string(),
    version: z.string(),
    description: z.string().optional(),
    port: z.number().optional(),
  }),
  prompts: z.object({
    directories: z.array(z.string()),
    watchForChanges: z.boolean(),
    cacheEnabled: z.boolean(),
    supportedFormats: z.array(z.string()),
    maxFileSize: z.number(),
  }),
  logging: z.object({
    level: z.enum(['debug', 'info', 'warn', 'error']),
    format: z.enum(['json', 'text']),
    file: z.string().optional(),
    console: z.boolean(),
  }),
  plugins: z.object({
    enabled: z.array(z.string()),
    config: z.record(z.unknown()),
    autoLoad: z.boolean(),
  }),
  performance: z.object({
    maxConcurrentTools: z.number(),
    requestTimeout: z.number(),
    cacheSize: z.number(),
  }),
});

export interface IConfigService {
  get<T = unknown>(path: string): T;
  set(path: string, value: unknown): void;
  getAll(): ServerConfig;
  watch(path: string, callback: (value: unknown) => void): void;
  reload(): Promise<void>;
}

export class ConfigService extends EventEmitter implements IConfigService {
  private config: ServerConfig;
  private configPath: string;
  private watchers: FSWatcher[] = [];

  constructor(configPath?: string) {
    super();
    this.configPath = configPath || this.findConfigFile();
    this.config = this.loadConfig();
    this.setupWatchers();
  }

  /**
   * 查找配置文件
   */
  private findConfigFile(): string {
    const possiblePaths = [
      './mcp-server.config.js',
      './mcp-server.config.json',
      './config/default.json',
      './config.json',
    ];

    for (const configPath of possiblePaths) {
      if (existsSync(configPath)) {
        return configPath;
      }
    }

    // 如果没有找到配置文件，使用默认配置
    return './config/default.json';
  }

  /**
   * 加载配置
   */
  private loadConfig(): ServerConfig {
    try {
      // 加载默认配置
      const defaultConfig = this.getDefaultConfig();

      // 加载环境变量配置
      const envConfig = this.loadFromEnvironment();

      // 加载文件配置
      const fileConfig = this.loadFromFile();

      // 合并配置（优先级：环境变量 > 文件配置 > 默认配置）
      const mergedConfig = this.mergeConfigs(defaultConfig, fileConfig, envConfig);

      // 验证配置
      this.validateConfig(mergedConfig);

      return mergedConfig;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to load configuration:', error);
      throw error;
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): ServerConfig {
    return {
      server: {
        name: 'mcp-prompt-server',
        version: '2.0.0',
        description: 'Modern MCP Prompt Server',
      },
      prompts: {
        directories: ['./src/prompts', './custom-prompts'],
        watchForChanges: true,
        cacheEnabled: true,
        supportedFormats: ['yaml', 'json', 'js', 'ts'],
        maxFileSize: 1024 * 1024, // 1MB
      },
      logging: {
        level: 'info',
        format: 'json',
        console: true,
      },
      plugins: {
        enabled: ['validation', 'cache'],
        config: {
          cache: {
            ttl: 3600,
            maxSize: 1000,
          },
        },
        autoLoad: true,
      },
      performance: {
        maxConcurrentTools: 10,
        requestTimeout: 30000,
        cacheSize: 1000,
      },
    };
  }

  /**
   * 从环境变量加载配置
   */
  private loadFromEnvironment(): Partial<ServerConfig> {
    const envConfig: Partial<ServerConfig> = {};

    // 服务器配置
    if (process.env['MCP_SERVER_NAME']) {
      envConfig.server = {
        ...envConfig.server,
        name: process.env['MCP_SERVER_NAME'],
        version: envConfig.server?.version || '2.0.0',
      };
    }

    if (process.env['MCP_SERVER_DESCRIPTION']) {
      envConfig.server = {
        ...envConfig.server,
        description: process.env['MCP_SERVER_DESCRIPTION'],
        name: envConfig.server?.name || 'mcp-prompt-server',
        version: envConfig.server?.version || '2.0.0',
      };
    }

    if (process.env['MCP_SERVER_PORT']) {
      const port = parseInt(process.env['MCP_SERVER_PORT'], 10);
      if (!isNaN(port)) {
        envConfig.server = {
          ...envConfig.server,
          port,
          name: envConfig.server?.name || 'mcp-prompt-server',
          version: envConfig.server?.version || '2.0.0',
        };
      }
    }

    // 日志配置
    if (process.env['MCP_LOG_LEVEL']) {
      envConfig.logging = {
        ...envConfig.logging,
        level: process.env['MCP_LOG_LEVEL'] as 'debug' | 'info' | 'warn' | 'error',
        format: envConfig.logging?.format || 'json',
        console: envConfig.logging?.console ?? true,
      };
    }

    if (process.env['MCP_LOG_FORMAT']) {
      envConfig.logging = {
        ...envConfig.logging,
        format: process.env['MCP_LOG_FORMAT'] as 'json' | 'text',
        level: envConfig.logging?.level || 'info',
        console: envConfig.logging?.console ?? true,
      };
    }

    if (process.env['MCP_LOG_FILE']) {
      envConfig.logging = {
        ...envConfig.logging,
        file: process.env['MCP_LOG_FILE'],
        level: envConfig.logging?.level || 'info',
        format: envConfig.logging?.format || 'json',
        console: envConfig.logging?.console ?? true,
      };
    }

    if (process.env['MCP_LOG_CONSOLE']) {
      envConfig.logging = {
        ...envConfig.logging,
        console: process.env['MCP_LOG_CONSOLE'].toLowerCase() === 'true',
        level: envConfig.logging?.level || 'info',
        format: envConfig.logging?.format || 'json',
      };
    }

    // Prompt目录配置
    if (process.env['MCP_PROMPT_DIRS']) {
      envConfig.prompts = {
        ...envConfig.prompts,
        directories: process.env['MCP_PROMPT_DIRS'].split(',').map(dir => dir.trim()),
        watchForChanges: envConfig.prompts?.watchForChanges ?? true,
        cacheEnabled: envConfig.prompts?.cacheEnabled ?? true,
        supportedFormats: envConfig.prompts?.supportedFormats || ['yaml', 'json'],
        maxFileSize: envConfig.prompts?.maxFileSize || 1024 * 1024,
      };
    }

    if (process.env['MCP_PROMPT_WATCH']) {
      envConfig.prompts = {
        ...envConfig.prompts,
        watchForChanges: process.env['MCP_PROMPT_WATCH'].toLowerCase() === 'true',
        directories: envConfig.prompts?.directories || ['./src/prompts'],
        cacheEnabled: envConfig.prompts?.cacheEnabled ?? true,
        supportedFormats: envConfig.prompts?.supportedFormats || ['yaml', 'json'],
        maxFileSize: envConfig.prompts?.maxFileSize || 1024 * 1024,
      };
    }

    if (process.env['MCP_PROMPT_CACHE']) {
      envConfig.prompts = {
        ...envConfig.prompts,
        cacheEnabled: process.env['MCP_PROMPT_CACHE'].toLowerCase() === 'true',
        directories: envConfig.prompts?.directories || ['./src/prompts'],
        watchForChanges: envConfig.prompts?.watchForChanges ?? true,
        supportedFormats: envConfig.prompts?.supportedFormats || ['yaml', 'json'],
        maxFileSize: envConfig.prompts?.maxFileSize || 1024 * 1024,
      };
    }

    if (process.env['MCP_PROMPT_MAX_SIZE']) {
      const maxSize = parseInt(process.env['MCP_PROMPT_MAX_SIZE'], 10);
      if (!isNaN(maxSize)) {
        envConfig.prompts = {
          ...envConfig.prompts,
          maxFileSize: maxSize,
          directories: envConfig.prompts?.directories || ['./src/prompts'],
          watchForChanges: envConfig.prompts?.watchForChanges ?? true,
          cacheEnabled: envConfig.prompts?.cacheEnabled ?? true,
          supportedFormats: envConfig.prompts?.supportedFormats || ['yaml', 'json'],
        };
      }
    }

    // 性能配置
    if (process.env['MCP_MAX_CONCURRENT_TOOLS']) {
      const maxTools = parseInt(process.env['MCP_MAX_CONCURRENT_TOOLS'], 10);
      if (!isNaN(maxTools)) {
        envConfig.performance = {
          ...envConfig.performance,
          maxConcurrentTools: maxTools,
          requestTimeout: envConfig.performance?.requestTimeout || 30000,
          cacheSize: envConfig.performance?.cacheSize || 1000,
        };
      }
    }

    if (process.env['MCP_REQUEST_TIMEOUT']) {
      const timeout = parseInt(process.env['MCP_REQUEST_TIMEOUT'], 10);
      if (!isNaN(timeout)) {
        envConfig.performance = {
          ...envConfig.performance,
          requestTimeout: timeout,
          maxConcurrentTools: envConfig.performance?.maxConcurrentTools || 10,
          cacheSize: envConfig.performance?.cacheSize || 1000,
        };
      }
    }

    if (process.env['MCP_CACHE_SIZE']) {
      const cacheSize = parseInt(process.env['MCP_CACHE_SIZE'], 10);
      if (!isNaN(cacheSize)) {
        envConfig.performance = {
          ...envConfig.performance,
          cacheSize,
          maxConcurrentTools: envConfig.performance?.maxConcurrentTools || 10,
          requestTimeout: envConfig.performance?.requestTimeout || 30000,
        };
      }
    }

    // 插件配置
    if (process.env['MCP_PLUGINS_ENABLED']) {
      envConfig.plugins = {
        ...envConfig.plugins,
        enabled: process.env['MCP_PLUGINS_ENABLED'].split(',').map(plugin => plugin.trim()),
        config: envConfig.plugins?.config || {},
        autoLoad: envConfig.plugins?.autoLoad ?? true,
      };
    }

    if (process.env['MCP_PLUGINS_AUTO_LOAD']) {
      envConfig.plugins = {
        ...envConfig.plugins,
        autoLoad: process.env['MCP_PLUGINS_AUTO_LOAD'].toLowerCase() === 'true',
        enabled: envConfig.plugins?.enabled || [],
        config: envConfig.plugins?.config || {},
      };
    }

    return envConfig;
  }

  /**
   * 从文件加载配置
   */
  private loadFromFile(): Partial<ServerConfig> {
    if (!existsSync(this.configPath)) {
      return {};
    }

    try {
      const ext = path.extname(this.configPath);
      let fileConfig: unknown;

      if (ext === '.js') {
        // 动态导入JS配置文件
        delete require.cache[require.resolve(path.resolve(this.configPath))];
        fileConfig = require(path.resolve(this.configPath));
      } else {
        // JSON配置文件
        const content = readFileSync(this.configPath, 'utf8');
        fileConfig = JSON.parse(content);
      }

      return fileConfig as Partial<ServerConfig>;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn(`Failed to load config file ${this.configPath}:`, error);
      return {};
    }
  }

  /**
   * 合并配置
   */
  private mergeConfigs(...configs: Partial<ServerConfig>[]): ServerConfig {
    const merged = {} as ServerConfig;

    for (const config of configs) {
      this.deepMerge(
        merged as unknown as Record<string, unknown>,
        config as unknown as Record<string, unknown>
      );
    }

    return merged;
  }

  /**
   * 深度合并对象
   */
  private deepMerge(target: Record<string, unknown>, source: Record<string, unknown>): void {
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        if (!target[key]) target[key] = {};
        this.deepMerge(
          target[key] as Record<string, unknown>,
          source[key] as Record<string, unknown>
        );
      } else {
        target[key] = source[key];
      }
    }
  }

  /**
   * 验证配置
   */
  private validateConfig(config: ServerConfig): void {
    try {
      ServerConfigSchema.parse(config);
    } catch (error) {
      throw new Error(`Configuration validation failed: ${error}`);
    }
  }

  /**
   * 设置文件监听
   */
  private setupWatchers(): void {
    if (existsSync(this.configPath)) {
      const watcher = watch(this.configPath, (eventType: string) => {
        if (eventType === 'change') {
          // eslint-disable-next-line no-console
          console.log('Configuration file changed, reloading...');
          this.reloadConfig();
        }
      });

      this.watchers.push(watcher);
    }
  }

  /**
   * 重新加载配置
   */
  private reloadConfig(): void {
    try {
      const oldConfig = { ...this.config };
      this.config = this.loadConfig();

      // 发出配置变更事件
      this.emit('config:changed', this.config, oldConfig);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to reload configuration:', error);
    }
  }

  /**
   * 获取配置值
   */
  get<T = unknown>(path: string): T {
    return this.getNestedValue(this.config as unknown as Record<string, unknown>, path) as T;
  }

  /**
   * 设置配置值
   */
  set(path: string, value: unknown): void {
    this.setNestedValue(this.config as unknown as Record<string, unknown>, path, value);
    this.emit('config:changed', path, value);
  }

  /**
   * 获取所有配置
   */
  getAll(): ServerConfig {
    return { ...this.config };
  }

  /**
   * 监听配置变更
   */
  watch(path: string, callback: (value: unknown) => void): void {
    this.on('config:changed', (changedPath: string, value: unknown) => {
      if (changedPath === path || changedPath.startsWith(`${path}.`)) {
        callback(value);
      }
    });
  }

  /**
   * 重新加载配置
   */
  async reload(): Promise<void> {
    this.reloadConfig();
  }

  /**
   * 获取嵌套值
   */
  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    return path.split('.').reduce((current: unknown, key: string) => {
      return (current as Record<string, unknown>)?.[key];
    }, obj);
  }

  /**
   * 设置嵌套值
   */
  private setNestedValue(obj: Record<string, unknown>, path: string, value: unknown): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current: Record<string, unknown>, key: string) => {
      if (!current[key]) current[key] = {};
      return current[key] as Record<string, unknown>;
    }, obj);

    target[lastKey] = value;
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.watchers.forEach(watcher => watcher.close());
    this.removeAllListeners();
  }
}
