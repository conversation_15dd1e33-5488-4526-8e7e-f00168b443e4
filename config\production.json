{"server": {"name": "my-prompt-mcp", "version": "2.0.0", "description": "Production MCP Prompt Server with Enterprise Features"}, "prompts": {"directories": ["./prompts", "/opt/my-prompt-mcp/prompts"], "watchForChanges": false, "cacheEnabled": true, "supportedFormats": ["yaml", "json"], "maxFileSize": 1048576}, "logging": {"level": "info", "format": "json", "file": "/var/log/my-prompt-mcp/server.log", "console": false}, "plugins": {"enabled": ["validation", "cache", "metrics", "authentication", "health-check"], "config": {"validation": {"strict": true, "allowExtraArgs": false}, "cache": {"ttl": 3600, "maxSize": 10000, "compression": true}, "metrics": {"enabled": true, "interval": 30000, "exportPath": "/var/lib/my-prompt-mcp/metrics", "retention": "7d"}, "authentication": {"enabled": true, "tokenExpiration": 86400000, "maxAttempts": 5, "lockoutDuration": 900000}, "health-check": {"enabled": true, "endpoint": "/health", "interval": 30000, "checks": ["memory", "disk", "prompts", "config", "authentication"]}}, "autoLoad": true}, "performance": {"maxConcurrentTools": 50, "requestTimeout": 30000, "cacheSize": 10000}}