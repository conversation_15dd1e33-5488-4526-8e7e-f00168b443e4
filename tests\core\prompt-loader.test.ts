/**
 * Prompt加载器基础测试
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { PromptLoader } from '../../src/core/prompt-loader';
import type { ServerConfig } from '../../src/types/config';

describe('PromptLoader', () => {
  let loader: PromptLoader;
  let mockConfig: ServerConfig;

  beforeEach(() => {
    mockConfig = {
      server: {
        name: 'test-server',
        version: '1.0.0',
      },
      prompts: {
        directories: ['./test-prompts'],
        watchForChanges: false,
        cacheEnabled: true,
        supportedFormats: ['yaml', 'json'],
        maxFileSize: 1024 * 1024,
      },
      logging: {
        level: 'error' as const,
        format: 'text' as const,
        console: false,
      },
      plugins: {
        enabled: [],
        config: {},
        autoLoad: false,
      },
      performance: {
        maxConcurrentTools: 5,
        requestTimeout: 5000,
        cacheSize: 100,
      },
    };

    loader = new PromptLoader(mockConfig);
  });

  afterEach(() => {
    loader.destroy();
  });

  describe('basic functionality', () => {
    it('should create PromptLoader instance', () => {
      expect(loader).toBeInstanceOf(PromptLoader);
    });

    it('should return empty prompts initially', () => {
      const prompts = loader.getLoadedPrompts();
      expect(prompts).toEqual([]);
    });

    it('should handle non-existent directories gracefully', async () => {
      const result = await loader.loadPrompts(['./non-existent-directory']);
      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('configuration', () => {
    it('should respect supported file formats', () => {
      expect(mockConfig.prompts.supportedFormats).toContain('yaml');
      expect(mockConfig.prompts.supportedFormats).toContain('json');
    });

    it('should have correct file size limit', () => {
      expect(mockConfig.prompts.maxFileSize).toBe(1024 * 1024);
    });
  });
});
