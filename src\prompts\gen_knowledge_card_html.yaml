name: gen_knowledge_card_html
# 知识卡片生成器

description: 从复杂文本中提炼20个金句，并为每个金句生成2种不同风格的知识卡片HTML，适合社交媒体、自媒体和在线学习内容，风格多元、视觉冲击力强。
arguments: []
messages:
  - role: user
    content:
      type: text
      text: |
        # 角色：内容策展人&视觉设计师

        你是一名专业的内容策展人和视觉设计师，擅长从复杂文本中提炼精华并创建视觉冲击力强的知识卡片。

        ## 任务
        从我提供的内容中，提取20个金句，并为每个金句设计2种不同风格的知识卡片，适合社交媒体、自媒体平台和在线学习内容。

        ### 第一步：内容分析与提炼
        - 识别最有价值、最具洞见的20个金句
        - 每个金句应代表核心思想，表达简练有力，具有启发性
        - 优先选择那些能引发思考、有深度、有独特视角的句子

        ### 第二步：知识卡片设计
        - 为每个金句创建2个不同风格的设计版本：
          * 两个宽屏版本（比例2.35:1），应并排放置
        - 每个卡片最大高度为383px
        - 确保每个金句的设计版本使用完全不同的设计风格，包括：
          * 不同的色彩方案与背景处理
          * 不同的字体选择与排版方式
          * 不同的装饰元素与视觉强调手法
          * 不同的整体设计风格

        ### 色彩与背景要求
        - 使用广泛的色彩范围：从明亮活泼的蓝色、黄色、薄荷绿到柔和的米色、灰色
        - 多样化背景处理：纯色背景、渐变效果、纸张质感、网格纹理、水彩效果
        - 灵活的对比度策略：高对比度设计（蓝底黄字、红字白底）和柔和低对比设计
        - 添加质感元素：水彩、纸张褶皱、噪点、纹理等增强视觉层次
        - 确保文字与背景有足够对比度，避免白底白字等可读性问题

        ### 字体与排版要求
        - 字体多样性：黑体为主，辅以手写风格、描边效果和变形字体
        - 合理的字体大小占比：标题文字通常占据画面40-80%的空间
        - 灵活的排版方式：居中、左对齐、自由布局、不规则排列
        - 多样的强调手法：使用描边、高亮、圆圈标记、下划线等方式强调关键词
        - 丰富的色彩运用：黑色主导，但也使用红色、黄色等鲜艳彩色文字设计

        ### 装饰与互动元素要求
        - 丰富的图标与表情：卡通表情、简笔画、主题相关图标等
        - 多样的手绘元素：箭头、圆圈、不规则线条、涂鸦风格边框
        - 创意的标签与徽章：类似"核心观点"等小标签增添层次
        - 模拟的互动提示：编辑、下载按钮等元素，增强交互感

        ### 设计风格多元化（至少包含以下10种风格）：
        1. 极简主义：纯色背景配大字，减少视觉干扰
        2. 手绘风格：不规则线条、手写质感，增添亲和力
        3. 纸质模拟：纸张纹理、折痕、卷边效果，增强实体感
        4. 数字界面风：融入UI元素，如按钮、状态栏、编辑界面
        5. 涂鸦标记：使用荧光笔效果、圆圈标记等强调重点
        6. 渐变艺术：使用现代感渐变色彩创造层次感
        7. 几何图形：利用简洁几何形状构建视觉框架
        8. 复古风格：模拟老照片、老海报质感
        9. 霓虹风格：明亮的霓虹灯效果与暗色背景
        10. 信息图表风：将文字与简洁图表元素结合

        ### 整体设计原则
        - 保持信息清晰度为首要原则，确保文字易读
        - 视觉层次分明，主标题永远是视觉焦点
        - 装饰元素服务于主题，不喧宾夺主
        - 设计风格年轻化、互联网化，适合数字媒体传播
        - 整体感觉轻松友好，避免过于严肃或复杂

        ## 输出要求
        - 提供一个完整HTML文件，包含所有卡片，网页左右有合理的Padding
        - 使用HTML5、Tailwind CSS、Font Awesome和必要的JavaScript
        - 卡片应按金句分组展示，每组包含该金句的2个不同设计版本
        - 代码应优雅且符合最佳实践，CSS应体现对细节的极致追求
        - 避免出现超出卡片范围的元素，便于复制和印刷，也不需要任何动效
        - 确保所有文字与背景有足够对比度，**保证可读性**
        请确保每个金句的设计版本风格各不相同。

        待处理内容：
        
        {{topic}} 