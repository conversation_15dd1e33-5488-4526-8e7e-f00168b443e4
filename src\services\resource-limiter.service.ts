/**
 * 资源限制服务 - 防止资源滥用和系统过载
 */

import { EventEmitter } from 'eventemitter3';
import type { ILogger } from './logger.service.js';
import type { ServerConfig } from '../types/config.js';

export interface ResourceLimits {
  maxMemoryMB: number;
  maxConcurrentOperations: number;
  maxFileSize: number;
  maxFilesPerDirectory: number;
  requestTimeoutMs: number;
  rateLimitPerMinute: number;
}

export interface ResourceUsage {
  memoryUsageMB: number;
  concurrentOperations: number;
  openFileHandles: number;
  requestsPerMinute: number;
}

export class ResourceLimiterService extends EventEmitter {
  private limits: ResourceLimits;
  private logger: ILogger;
  private concurrentOperations = 0;
  private requestCounts = new Map<string, number[]>();
  private operationQueue: Array<() => Promise<void>> = [];

  constructor(config: ServerConfig, logger: ILogger) {
    super();
    this.logger = logger.forComponent('ResourceLimiter');

    this.limits = {
      maxMemoryMB: 1024, // 1GB
      maxConcurrentOperations: config.performance.maxConcurrentTools,
      maxFileSize: config.prompts.maxFileSize,
      maxFilesPerDirectory: 1000,
      requestTimeoutMs: config.performance.requestTimeout,
      rateLimitPerMinute: 100,
    };

    // 定期清理请求计数
    setInterval(() => this.cleanupRequestCounts(), 60000);
  }

  /**
   * 检查内存使用限制
   */
  checkMemoryLimit(): boolean {
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;

    if (memoryUsageMB > this.limits.maxMemoryMB) {
      this.logger.warn('Memory limit exceeded', {
        currentUsageMB: Math.round(memoryUsageMB),
        limitMB: this.limits.maxMemoryMB,
      });

      this.emit('limit:exceeded', 'memory', {
        current: memoryUsageMB,
        limit: this.limits.maxMemoryMB,
      });

      return false;
    }

    return true;
  }

  /**
   * 检查并发操作限制
   */
  async acquireOperationSlot(): Promise<boolean> {
    if (this.concurrentOperations >= this.limits.maxConcurrentOperations) {
      this.logger.warn('Concurrent operations limit reached', {
        current: this.concurrentOperations,
        limit: this.limits.maxConcurrentOperations,
      });

      return false;
    }

    this.concurrentOperations++;
    this.logger.debug('Operation slot acquired', {
      current: this.concurrentOperations,
      limit: this.limits.maxConcurrentOperations,
    });

    return true;
  }

  /**
   * 释放操作槽位
   */
  releaseOperationSlot(): void {
    if (this.concurrentOperations > 0) {
      this.concurrentOperations--;
      this.logger.debug('Operation slot released', {
        current: this.concurrentOperations,
      });
    }
  }

  /**
   * 执行带资源限制的操作
   */
  async executeWithLimits<T>(
    operation: () => Promise<T>,
    operationName: string,
    clientId?: string
  ): Promise<T> {
    // 检查内存限制
    if (!this.checkMemoryLimit()) {
      throw new Error('Memory limit exceeded');
    }

    // 检查速率限制
    if (clientId && !this.checkRateLimit(clientId)) {
      throw new Error('Rate limit exceeded');
    }

    // 获取操作槽位
    if (!(await this.acquireOperationSlot())) {
      throw new Error('Too many concurrent operations');
    }

    const startTime = Date.now();
    let timeoutHandle: NodeJS.Timeout | undefined;

    try {
      // 设置超时
      const timeoutPromise = new Promise<never>((_, reject) => {
        timeoutHandle = setTimeout(() => {
          reject(new Error(`Operation timeout: ${operationName}`));
        }, this.limits.requestTimeoutMs);
      });

      // 执行操作
      const result = await Promise.race([operation(), timeoutPromise]);

      const duration = Date.now() - startTime;
      this.logger.debug('Operation completed', {
        operation: operationName,
        duration,
        clientId,
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Operation failed', error as Error, {
        operation: operationName,
        duration,
        clientId,
      });
      throw error;
    } finally {
      if (timeoutHandle) {
        clearTimeout(timeoutHandle);
      }
      this.releaseOperationSlot();
    }
  }

  /**
   * 检查速率限制
   */
  checkRateLimit(clientId: string): boolean {
    const now = Date.now();
    const windowStart = now - 60000; // 1分钟窗口

    if (!this.requestCounts.has(clientId)) {
      this.requestCounts.set(clientId, []);
    }

    const requests = this.requestCounts.get(clientId)!;

    // 移除过期的请求记录
    const validRequests = requests.filter(timestamp => timestamp > windowStart);

    if (validRequests.length >= this.limits.rateLimitPerMinute) {
      this.logger.warn('Rate limit exceeded', {
        clientId,
        requestCount: validRequests.length,
        limit: this.limits.rateLimitPerMinute,
      });

      this.emit('limit:exceeded', 'rate', {
        clientId,
        current: validRequests.length,
        limit: this.limits.rateLimitPerMinute,
      });

      return false;
    }

    // 记录当前请求
    validRequests.push(now);
    this.requestCounts.set(clientId, validRequests);

    return true;
  }

  /**
   * 检查文件大小限制
   */
  checkFileSize(size: number): boolean {
    if (size > this.limits.maxFileSize) {
      this.logger.warn('File size limit exceeded', {
        size,
        limit: this.limits.maxFileSize,
      });

      this.emit('limit:exceeded', 'fileSize', {
        current: size,
        limit: this.limits.maxFileSize,
      });

      return false;
    }

    return true;
  }

  /**
   * 检查目录文件数量限制
   */
  checkDirectoryFileCount(count: number): boolean {
    if (count > this.limits.maxFilesPerDirectory) {
      this.logger.warn('Directory file count limit exceeded', {
        count,
        limit: this.limits.maxFilesPerDirectory,
      });

      this.emit('limit:exceeded', 'directoryFiles', {
        current: count,
        limit: this.limits.maxFilesPerDirectory,
      });

      return false;
    }

    return true;
  }

  /**
   * 获取当前资源使用情况
   */
  getResourceUsage(): ResourceUsage {
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;

    // 计算最近一分钟的总请求数
    const now = Date.now();
    const windowStart = now - 60000;
    let totalRequests = 0;

    for (const requests of this.requestCounts.values()) {
      totalRequests += requests.filter(timestamp => timestamp > windowStart).length;
    }

    return {
      memoryUsageMB: Math.round(memoryUsageMB),
      concurrentOperations: this.concurrentOperations,
      openFileHandles: this.getOpenFileHandles(),
      requestsPerMinute: totalRequests,
    };
  }

  /**
   * 获取打开的文件句柄数量（近似值）
   */
  private getOpenFileHandles(): number {
    try {
      // 在Node.js中，这是一个近似值
      return process
        .getActiveResourcesInfo()
        .filter(resource => resource.includes('File') || resource.includes('fs')).length;
    } catch {
      return 0;
    }
  }

  /**
   * 清理过期的请求计数
   */
  private cleanupRequestCounts(): void {
    const now = Date.now();
    const windowStart = now - 60000;

    for (const [clientId, requests] of this.requestCounts.entries()) {
      const validRequests = requests.filter(timestamp => timestamp > windowStart);

      if (validRequests.length === 0) {
        this.requestCounts.delete(clientId);
      } else {
        this.requestCounts.set(clientId, validRequests);
      }
    }
  }

  /**
   * 更新资源限制
   */
  updateLimits(newLimits: Partial<ResourceLimits>): void {
    this.limits = { ...this.limits, ...newLimits };
    this.logger.info('Resource limits updated', { limits: this.limits });
  }

  /**
   * 获取当前限制配置
   */
  getLimits(): ResourceLimits {
    return { ...this.limits };
  }

  /**
   * 强制垃圾回收（如果可用）
   */
  forceGarbageCollection(): void {
    if (global.gc) {
      global.gc();
      this.logger.debug('Forced garbage collection');
    } else {
      this.logger.warn('Garbage collection not available (run with --expose-gc)');
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.requestCounts.clear();
    this.operationQueue.length = 0;
    this.removeAllListeners();
  }
}
