/**
 * 类型定义测试
 */

import { describe, it, expect } from '@jest/globals';
import type { PromptTemplate, ServerConfig } from '../src/types/index';

describe('Type Definitions', () => {
  it('should have valid PromptTemplate type', () => {
    const prompt: PromptTemplate = {
      name: 'test-prompt',
      description: 'Test prompt',
      messages: [
        {
          role: 'user',
          content: {
            type: 'text',
            text: 'Hello world',
          },
        },
      ],
    };

    expect(prompt.name).toBe('test-prompt');
    expect(prompt.description).toBe('Test prompt');
    expect(prompt.messages).toHaveLength(1);
  });

  it('should have valid ServerConfig type', () => {
    const config: ServerConfig = {
      server: {
        name: 'test-server',
        version: '1.0.0',
      },
      prompts: {
        directories: ['./prompts'],
        watchForChanges: true,
        cacheEnabled: true,
        supportedFormats: ['yaml', 'json'],
        maxFileSize: 1024 * 1024,
      },
      logging: {
        level: 'info',
        format: 'json',
        console: true,
      },
      plugins: {
        enabled: ['validation'],
        config: {},
        autoLoad: true,
      },
      performance: {
        maxConcurrentTools: 10,
        requestTimeout: 30000,
        cacheSize: 1000,
      },
    };

    expect(config.server.name).toBe('test-server');
    expect(config.prompts.directories).toContain('./prompts');
    expect(config.logging.level).toBe('info');
  });
});
