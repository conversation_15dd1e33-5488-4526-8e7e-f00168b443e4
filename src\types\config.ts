/**
 * 配置相关类型定义
 */

export interface ServerConfig {
  server: ServerSettings;
  prompts: PromptSettings;
  logging: LoggingSettings;
  plugins: PluginSettings;
  performance: PerformanceSettings;
}

export interface ServerSettings {
  name: string;
  version: string;
  description?: string;
  port?: number;
}

export interface PromptSettings {
  directories: string[];
  watchForChanges: boolean;
  cacheEnabled: boolean;
  supportedFormats: string[];
  maxFileSize: number;
}

export interface LoggingSettings {
  level: 'debug' | 'info' | 'warn' | 'error';
  format: 'json' | 'text';
  file?: string;
  console: boolean;
}

export interface PluginSettings {
  enabled: string[];
  config: Record<string, unknown>;
  autoLoad: boolean;
}

export interface PerformanceSettings {
  maxConcurrentTools: number;
  requestTimeout: number;
  cacheSize: number;
}

export interface ConfigSource {
  type: 'file' | 'env' | 'default';
  path?: string;
  priority: number;
}
