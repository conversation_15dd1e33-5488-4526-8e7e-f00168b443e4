{"server": {"name": "mcp-prompt-server-dev", "version": "2.0.0", "description": "MCP Prompt Server - Development Environment", "port": 3000}, "prompts": {"directories": ["./src/prompts", "./dev-prompts", "./examples/prompts"], "watchForChanges": true, "cacheEnabled": false, "supportedFormats": ["yaml", "json", "js", "ts"], "maxFileSize": 5242880}, "logging": {"level": "debug", "format": "text", "file": "./logs/development.log", "console": true}, "plugins": {"enabled": ["validation"], "config": {"validation": {"strict": false, "allowExtraArgs": true}}, "autoLoad": true}, "performance": {"maxConcurrentTools": 5, "requestTimeout": 60000, "cacheSize": 100}}