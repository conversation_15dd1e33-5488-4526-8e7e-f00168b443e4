{"server": {"name": "mcp-prompt-server", "version": "2.0.0", "description": "MCP Prompt Server - Production Environment"}, "prompts": {"directories": ["./prompts"], "watchForChanges": false, "cacheEnabled": true, "supportedFormats": ["yaml", "json"], "maxFileSize": 1048576}, "logging": {"level": "info", "format": "json", "file": "./logs/production.log", "console": false}, "plugins": {"enabled": ["validation", "cache", "metrics"], "config": {"validation": {"strict": true, "allowExtraArgs": false}, "cache": {"ttl": 3600, "maxSize": 10000}, "metrics": {"enabled": true, "interval": 30000, "exportPath": "./metrics"}}, "autoLoad": true}, "performance": {"maxConcurrentTools": 20, "requestTimeout": 30000, "cacheSize": 5000}}