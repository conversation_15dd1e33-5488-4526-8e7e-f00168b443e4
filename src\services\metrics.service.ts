/**
 * 性能监控服务 - 收集和报告系统性能指标
 */

import { EventEmitter } from 'eventemitter3';
import type { ILogger } from './logger.service.js';

export interface MetricValue {
  value: number;
  timestamp: number;
  labels?: Record<string, string>;
}

export interface PerformanceMetrics {
  // 系统指标
  memoryUsage: {
    rss: number;
    heapUsed: number;
    heapTotal: number;
    external: number;
  };
  cpuUsage: {
    user: number;
    system: number;
  };
  uptime: number;

  // 业务指标
  totalPrompts: number;
  totalTools: number;
  toolCalls: number;
  toolCallsPerMinute: number;
  averageResponseTime: number;
  errorRate: number;

  // 缓存指标
  cacheHits: number;
  cacheMisses: number;
  cacheHitRate: number;
}

export interface IMetricsService {
  recordMetric(name: string, value: number, labels?: Record<string, string>): void;
  recordDuration(name: string, startTime: number, labels?: Record<string, string>): void;
  incrementCounter(name: string, labels?: Record<string, string>): void;
  getMetrics(): PerformanceMetrics;
  startCollection(): void;
  stopCollection(): void;
  destroy(): void;
}

export class MetricsService extends EventEmitter implements IMetricsService {
  private metrics = new Map<string, MetricValue[]>();
  private counters = new Map<string, number>();
  private durations = new Map<string, number[]>();
  private logger: ILogger;
  private collectionInterval: NodeJS.Timeout | undefined;
  private startTime: number;
  private lastCpuUsage = process.cpuUsage();

  constructor(logger: ILogger) {
    super();
    this.logger = logger.forComponent('MetricsService');
    this.startTime = Date.now();
  }

  recordMetric(name: string, value: number, labels: Record<string, string> = {}): void {
    const metric: MetricValue = {
      value,
      timestamp: Date.now(),
      labels,
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const values = this.metrics.get(name)!;
    values.push(metric);

    // 保持最近1000个数据点
    if (values.length > 1000) {
      values.shift();
    }

    this.emit('metric:recorded', name, metric);
  }

  recordDuration(name: string, startTime: number, labels: Record<string, string> = {}): void {
    const duration = Date.now() - startTime;

    if (!this.durations.has(name)) {
      this.durations.set(name, []);
    }

    const durations = this.durations.get(name)!;
    durations.push(duration);

    // 保持最近1000个数据点
    if (durations.length > 1000) {
      durations.shift();
    }

    this.recordMetric(`${name}_duration`, duration, labels);
    this.logger.performance(name, duration, labels);
  }

  incrementCounter(name: string, labels: Record<string, string> = {}): void {
    const key = `${name}:${JSON.stringify(labels)}`;
    const current = this.counters.get(key) || 0;
    this.counters.set(key, current + 1);

    this.recordMetric(name, current + 1, labels);
  }

  getMetrics(): PerformanceMetrics {
    const memoryUsage = process.memoryUsage();
    const currentCpuUsage = process.cpuUsage(this.lastCpuUsage);
    this.lastCpuUsage = process.cpuUsage();

    // 计算工具调用速率
    const toolCallsMetric = this.metrics.get('tool_calls') || [];
    const recentCalls = toolCallsMetric.filter(
      m => Date.now() - m.timestamp < 60000 // 最近1分钟
    );

    // 计算平均响应时间
    const responseTimes = this.durations.get('tool_call') || [];
    const averageResponseTime =
      responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0;

    // 计算错误率
    const totalCalls = this.counters.get('tool_calls:{}') || 0;
    const errorCalls = this.counters.get('tool_errors:{}') || 0;
    const errorRate = totalCalls > 0 ? (errorCalls / totalCalls) * 100 : 0;

    // 计算缓存命中率
    const cacheHits = this.counters.get('cache_hits:{}') || 0;
    const cacheMisses = this.counters.get('cache_misses:{}') || 0;
    const totalCacheRequests = cacheHits + cacheMisses;
    const cacheHitRate = totalCacheRequests > 0 ? (cacheHits / totalCacheRequests) * 100 : 0;

    return {
      memoryUsage: {
        rss: memoryUsage.rss,
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
      },
      cpuUsage: {
        user: currentCpuUsage.user / 1000, // 转换为毫秒
        system: currentCpuUsage.system / 1000,
      },
      uptime: Date.now() - this.startTime,
      totalPrompts: this.counters.get('prompts_loaded:{}') || 0,
      totalTools: this.counters.get('tools_registered:{}') || 0,
      toolCalls: totalCalls,
      toolCallsPerMinute: recentCalls.length,
      averageResponseTime,
      errorRate,
      cacheHits,
      cacheMisses,
      cacheHitRate,
    };
  }

  startCollection(): void {
    if (this.collectionInterval) {
      return;
    }

    this.logger.info('Starting metrics collection');

    // 每30秒收集一次系统指标
    this.collectionInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // 立即收集一次
    this.collectSystemMetrics();
  }

  stopCollection(): void {
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = undefined;
      this.logger.info('Stopped metrics collection');
    }
  }

  private collectSystemMetrics(): void {
    const metrics = this.getMetrics();

    // 记录系统指标
    this.recordMetric('memory_rss', metrics.memoryUsage.rss);
    this.recordMetric('memory_heap_used', metrics.memoryUsage.heapUsed);
    this.recordMetric('memory_heap_total', metrics.memoryUsage.heapTotal);
    this.recordMetric('cpu_user', metrics.cpuUsage.user);
    this.recordMetric('cpu_system', metrics.cpuUsage.system);

    // 检查内存使用情况
    const memoryUsageMB = metrics.memoryUsage.heapUsed / 1024 / 1024;
    if (memoryUsageMB > 500) {
      // 超过500MB警告
      this.logger.warn('High memory usage detected', {
        memoryUsageMB: Math.round(memoryUsageMB),
        threshold: 500,
      });
    }

    // 检查错误率
    if (metrics.errorRate > 5) {
      // 错误率超过5%警告
      this.logger.warn('High error rate detected', {
        errorRate: metrics.errorRate,
        threshold: 5,
      });
    }

    this.emit('metrics:collected', metrics);
  }

  /**
   * 创建性能计时器
   */
  createTimer(name: string, labels?: Record<string, string>): () => void {
    const startTime = Date.now();
    return () => {
      this.recordDuration(name, startTime, labels);
    };
  }

  /**
   * 获取指标历史数据
   */
  getMetricHistory(name: string, minutes: number = 60): MetricValue[] {
    const metrics = this.metrics.get(name) || [];
    const cutoff = Date.now() - minutes * 60 * 1000;
    return metrics.filter(m => m.timestamp > cutoff);
  }

  /**
   * 获取计数器值
   */
  getCounter(name: string, labels: Record<string, string> = {}): number {
    const key = `${name}:${JSON.stringify(labels)}`;
    return this.counters.get(key) || 0;
  }

  /**
   * 重置所有指标
   */
  reset(): void {
    this.metrics.clear();
    this.counters.clear();
    this.durations.clear();
    this.logger.info('Metrics reset');
  }

  /**
   * 导出指标数据
   */
  exportMetrics(): {
    metrics: Record<string, MetricValue[]>;
    counters: Record<string, number>;
    durations: Record<string, number[]>;
  } {
    return {
      metrics: Object.fromEntries(this.metrics),
      counters: Object.fromEntries(this.counters),
      durations: Object.fromEntries(this.durations),
    };
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.stopCollection();
    this.removeAllListeners();
    this.reset();
  }
}
