/**
 * 事件系统相关类型定义
 */

import type { PromptTemplate } from './prompt.js';

export interface ServerEvents {
  'prompt:loaded': (_prompt: PromptTemplate) => void;
  'prompt:unloaded': (_name: string) => void;
  'prompt:changed': (_name: string, _prompt: PromptTemplate) => void;
  'prompts:loaded': (_prompts: PromptTemplate[]) => void;
  'prompts:reloaded': (_prompts: PromptTemplate[]) => void;
  'tool:registered': (_name: string, _tool: unknown) => void;
  'tool:unregistered': (_name: string) => void;
  'tool:called': (_name: string, _args: unknown, _result: unknown) => void;
  'tool:error': (_name: string, _args: unknown, _error: Error) => void;
  'server:started': () => void;
  'server:stopped': () => void;
  'server:error': (_error: Error) => void;
  'config:changed': (_path: string, _value: unknown) => void;
  'plugin:loaded': (_name: string) => void;
  'plugin:unloaded': (_name: string) => void;
}

export type EventName = keyof ServerEvents;
export type EventHandler<T extends EventName> = ServerEvents[T];
