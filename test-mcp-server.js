#!/usr/bin/env node

/**
 * 完整的MCP服务器协议测试套件
 * 测试所有MCP协议功能，确保服务器完全符合MCP规范
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { EventEmitter } from 'events';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class MCPTester extends EventEmitter {
  constructor() {
    super();
    this.server = null;
    this.messageId = 0;
    this.responses = new Map();
    this.testResults = [];
    this.outputBuffer = '';
  }

  /**
   * 启动MCP服务器
   */
  async startServer() {
    const serverPath = join(__dirname, 'dist', 'index.js');
    console.log(`🚀 Starting MCP Server: ${serverPath}`);

    this.server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: __dirname,
      env: { ...process.env, NODE_ENV: 'test' }
    });

    this.server.stdout.on('data', (data) => {
      this.handleServerOutput(data);
    });

    this.server.stderr.on('data', (data) => {
      const message = data.toString();
      if (message.trim()) {
        console.log('📤 STDERR:', message.trim());
      }
    });

    this.server.on('close', (code) => {
      console.log(`🏁 Server process exited with code ${code}`);
      this.emit('server:closed', code);
    });

    this.server.on('error', (error) => {
      console.error('❌ Server error:', error);
      this.emit('server:error', error);
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  /**
   * 处理服务器输出
   */
  handleServerOutput(data) {
    this.outputBuffer += data.toString();

    // 处理完整的JSON消息
    const lines = this.outputBuffer.split('\n');
    this.outputBuffer = lines.pop() || ''; // 保留不完整的行

    for (const line of lines) {
      if (line.trim()) {
        try {
          const message = JSON.parse(line.trim());
          this.handleMCPMessage(message);
        } catch (error) {
          console.log('📤 Non-JSON output:', line.trim());
        }
      }
    }
  }

  /**
   * 处理MCP消息
   */
  handleMCPMessage(message) {
    console.log('📨 Received MCP message:', JSON.stringify(message, null, 2));

    if (message.id && this.responses.has(message.id)) {
      const resolver = this.responses.get(message.id);
      this.responses.delete(message.id);
      resolver(message);
    }

    this.emit('message', message);
  }

  /**
   * 发送MCP消息
   */
  async sendMessage(method, params = {}) {
    const id = ++this.messageId;
    const message = {
      jsonrpc: '2.0',
      id,
      method,
      params
    };

    console.log('📤 Sending MCP message:', JSON.stringify(message, null, 2));

    return new Promise((resolve, reject) => {
      this.responses.set(id, resolve);

      // 设置超时
      setTimeout(() => {
        if (this.responses.has(id)) {
          this.responses.delete(id);
          reject(new Error(`Timeout waiting for response to ${method}`));
        }
      }, 10000);

      this.server.stdin.write(JSON.stringify(message) + '\n');
    });
  }

  /**
   * 发送通知消息（无响应）
   */
  sendNotification(method, params = {}) {
    const message = {
      jsonrpc: '2.0',
      method,
      params
    };

    console.log('📤 Sending notification:', JSON.stringify(message, null, 2));
    this.server.stdin.write(JSON.stringify(message) + '\n');
  }

  /**
   * 执行测试用例
   */
  async runTest(name, testFn) {
    console.log(`\n🧪 Running test: ${name}`);
    try {
      await testFn();
      console.log(`✅ Test passed: ${name}`);
      this.testResults.push({ name, status: 'passed' });
    } catch (error) {
      console.error(`❌ Test failed: ${name}`, error.message);
      this.testResults.push({ name, status: 'failed', error: error.message });
    }
  }

  /**
   * 运行完整的测试套件
   */
  async runFullTestSuite() {
    try {
      await this.startServer();

      // 测试1: 初始化协议
      await this.runTest('Initialize Protocol', async () => {
        const response = await this.sendMessage('initialize', {
          protocolVersion: '2024-11-05',
          capabilities: {
            roots: {
              listChanged: true
            },
            sampling: {}
          },
          clientInfo: {
            name: 'mcp-test-client',
            version: '1.0.0'
          }
        });

        if (!response.result) {
          throw new Error('No result in initialize response');
        }

        if (!response.result.capabilities) {
          throw new Error('No capabilities in initialize response');
        }

        console.log('✅ Server capabilities:', JSON.stringify(response.result.capabilities, null, 2));
      });

      // 测试2: 发送initialized通知
      await this.runTest('Send Initialized Notification', async () => {
        this.sendNotification('notifications/initialized');
        // 等待一下确保通知被处理
        await new Promise(resolve => setTimeout(resolve, 500));
      });

      // 测试3: 列出可用的prompts
      await this.runTest('List Prompts', async () => {
        const response = await this.sendMessage('prompts/list');

        if (!response.result) {
          throw new Error('No result in prompts/list response');
        }

        if (!Array.isArray(response.result.prompts)) {
          throw new Error('Prompts should be an array');
        }

        console.log(`✅ Found ${response.result.prompts.length} prompts`);

        // 验证每个prompt的结构
        for (const prompt of response.result.prompts) {
          if (!prompt.name || !prompt.description) {
            throw new Error(`Invalid prompt structure: ${JSON.stringify(prompt)}`);
          }
        }
      });

      // 测试4: 获取特定prompt
      await this.runTest('Get Specific Prompt', async () => {
        // 先获取prompt列表
        const listResponse = await this.sendMessage('prompts/list');
        const prompts = listResponse.result.prompts;

        if (prompts.length === 0) {
          throw new Error('No prompts available for testing');
        }

        const firstPrompt = prompts[0];
        const response = await this.sendMessage('prompts/get', {
          name: firstPrompt.name,
          arguments: {}
        });

        if (!response.result) {
          throw new Error('No result in prompts/get response');
        }

        if (!Array.isArray(response.result.messages)) {
          throw new Error('Messages should be an array');
        }

        console.log(`✅ Retrieved prompt "${firstPrompt.name}" with ${response.result.messages.length} messages`);
      });

      // 测试5: 列出可用的tools
      await this.runTest('List Tools', async () => {
        const response = await this.sendMessage('tools/list');

        if (!response.result) {
          throw new Error('No result in tools/list response');
        }

        if (!Array.isArray(response.result.tools)) {
          throw new Error('Tools should be an array');
        }

        console.log(`✅ Found ${response.result.tools.length} tools`);

        // 验证每个tool的结构
        for (const tool of response.result.tools) {
          if (!tool.name || !tool.description) {
            throw new Error(`Invalid tool structure: ${JSON.stringify(tool)}`);
          }
        }
      });

      // 测试6: 调用工具（如果有可用的工具）
      await this.runTest('Call Tool', async () => {
        const listResponse = await this.sendMessage('tools/list');
        const tools = listResponse.result.tools;

        if (tools.length === 0) {
          console.log('⚠️  No tools available, skipping tool call test');
          return;
        }

        const firstTool = tools[0];
        const response = await this.sendMessage('tools/call', {
          name: firstTool.name,
          arguments: {}
        });

        if (!response.result) {
          throw new Error('No result in tools/call response');
        }

        if (!Array.isArray(response.result.content)) {
          throw new Error('Tool result content should be an array');
        }

        console.log(`✅ Called tool "${firstTool.name}" successfully`);
      });

      // 测试7: 测试错误处理
      await this.runTest('Error Handling', async () => {
        try {
          await this.sendMessage('invalid/method');
          throw new Error('Should have received an error for invalid method');
        } catch (error) {
          if (error.message.includes('Timeout')) {
            throw error; // 重新抛出超时错误
          }
          // 期望的错误，测试通过
          console.log('✅ Server correctly handled invalid method');
        }
      });

      // 测试8: 测试资源管理
      await this.runTest('Resource Management', async () => {
        try {
          const response = await this.sendMessage('resources/list');

          if (response.result) {
            console.log(`✅ Resources endpoint available with ${response.result.resources?.length || 0} resources`);
          } else {
            console.log('ℹ️  Resources endpoint not implemented (optional)');
          }
        } catch (error) {
          if (error.message.includes('Timeout')) {
            console.log('ℹ️  Resources endpoint not implemented (optional)');
          } else {
            throw error;
          }
        }
      });

    } finally {
      await this.stopServer();
    }
  }

  /**
   * 停止服务器
   */
  async stopServer() {
    if (this.server) {
      console.log('\n🛑 Stopping server...');
      this.server.kill('SIGTERM');

      // 等待服务器关闭
      await new Promise(resolve => {
        this.server.on('close', resolve);
        setTimeout(resolve, 5000); // 最多等待5秒
      });
    }
  }

  /**
   * 打印测试结果
   */
  printResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('=' .repeat(50));

    let passed = 0;
    let failed = 0;

    for (const result of this.testResults) {
      const status = result.status === 'passed' ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }

      if (result.status === 'passed') passed++;
      else failed++;
    }

    console.log('=' .repeat(50));
    console.log(`Total: ${this.testResults.length}, Passed: ${passed}, Failed: ${failed}`);

    if (failed === 0) {
      console.log('🎉 All tests passed! MCP server is fully functional.');
    } else {
      console.log(`⚠️  ${failed} test(s) failed. Please review the errors above.`);
    }

    return failed === 0;
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🧪 Starting Complete MCP Server Test Suite');
  console.log('=' .repeat(60));

  const tester = new MCPTester();

  try {
    await tester.runFullTestSuite();
    const allPassed = tester.printResults();
    process.exit(allPassed ? 0 : 1);
  } catch (error) {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  }
}

main().catch(console.error);
